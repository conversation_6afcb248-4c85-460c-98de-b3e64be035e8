package com.suite.connector.repository;

import com.suite.connector.model.entity.SuiteOffset;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * SuiteOffset Repository 测试类
 * 
 * <AUTHOR> Connector Team
 */
@DataJpaTest
@ActiveProfiles("test")
class SuiteOffsetRepositoryTest {

    @Autowired
    private SuiteOffsetRepository suiteOffsetRepository;

    private SuiteOffset testOffset;

    @BeforeEach
    void setUp() {
        testOffset = SuiteOffset.builder()
                .suiteId("test_suite_001")
                .deptId("dept_001")
                .hostCode("host_001")
                .dataType("user_data")
                .subType("basic_info")
                .offsetValue(100L)
                .firstTime(LocalDateTime.now().minusHours(1))
                .lastTime(LocalDateTime.now())
                .opStatus("SUCCESS")
                .build();
    }

    @Test
    void testSaveAndFindById() {
        // 保存实体
        SuiteOffset saved = suiteOffsetRepository.save(testOffset);
        
        // 验证保存成功
        assertThat(saved.getId()).isNotNull();
        assertThat(saved.getSuiteId()).isEqualTo("test_suite_001");
        
        // 根据ID查找
        Optional<SuiteOffset> found = suiteOffsetRepository.findById(saved.getId());
        assertThat(found).isPresent();
        assertThat(found.get().getSuiteId()).isEqualTo("test_suite_001");
    }

    @Test
    void testFindBySuiteId() {
        // 保存测试数据
        suiteOffsetRepository.save(testOffset);
        
        // 创建另一个相同套件ID的记录
        SuiteOffset anotherOffset = SuiteOffset.builder()
                .suiteId("test_suite_001")
                .deptId("dept_002")
                .hostCode("host_002")
                .dataType("user_data")
                .subType("detail_info")
                .offsetValue(200L)
                .opStatus("SUCCESS")
                .build();
        suiteOffsetRepository.save(anotherOffset);
        
        // 查找指定套件ID的所有记录
        List<SuiteOffset> results = suiteOffsetRepository.findBySuiteId("test_suite_001");
        
        assertThat(results).hasSize(2);
        assertThat(results).extracting(SuiteOffset::getSuiteId)
                          .containsOnly("test_suite_001");
    }

    @Test
    void testFindSpecificRecord() {
        // 保存测试数据
        suiteOffsetRepository.save(testOffset);
        
        // 查找特定记录
        Optional<SuiteOffset> found = suiteOffsetRepository
                .findBySuiteIdAndDeptIdAndHostCodeAndDataTypeAndSubType(
                        "test_suite_001", "dept_001", "host_001", "user_data", "basic_info");
        
        assertThat(found).isPresent();
        assertThat(found.get().getOffsetValue()).isEqualTo(100L);
    }

    @Test
    void testFindByStatus() {
        // 保存成功状态的记录
        suiteOffsetRepository.save(testOffset);
        
        // 保存失败状态的记录
        SuiteOffset failedOffset = SuiteOffset.builder()
                .suiteId("test_suite_002")
                .deptId("dept_001")
                .hostCode("host_001")
                .dataType("user_data")
                .subType("basic_info")
                .offsetValue(0L)
                .opStatus("FAILED")
                .errMsg("Test error")
                .build();
        suiteOffsetRepository.save(failedOffset);
        
        // 查找成功状态的记录
        List<SuiteOffset> successRecords = suiteOffsetRepository.findByOpStatus("SUCCESS");
        assertThat(successRecords).hasSize(1);
        assertThat(successRecords.get(0).getSuiteId()).isEqualTo("test_suite_001");
        
        // 查找失败状态的记录
        List<SuiteOffset> failedRecords = suiteOffsetRepository.findByOpStatus("FAILED");
        assertThat(failedRecords).hasSize(1);
        assertThat(failedRecords.get(0).getSuiteId()).isEqualTo("test_suite_002");
    }

    @Test
    void testUpdateProgress() {
        // 保存初始记录
        SuiteOffset saved = suiteOffsetRepository.save(testOffset);
        
        // 更新进度
        LocalDateTime newSyncTime = LocalDateTime.now();
        saved.updateProgress(200L, newSyncTime, "SUCCESS", null);
        
        // 保存更新
        suiteOffsetRepository.save(saved);
        
        // 验证更新
        Optional<SuiteOffset> updated = suiteOffsetRepository.findById(saved.getId());
        assertThat(updated).isPresent();
        assertThat(updated.get().getOffsetValue()).isEqualTo(200L);
        assertThat(updated.get().getLastTime()).isEqualTo(newSyncTime);
    }

    @Test
    void testMarkAsFailed() {
        // 保存初始记录
        SuiteOffset saved = suiteOffsetRepository.save(testOffset);
        
        // 标记为失败
        saved.markAsFailed("Connection timeout");
        suiteOffsetRepository.save(saved);
        
        // 验证失败状态
        Optional<SuiteOffset> failed = suiteOffsetRepository.findById(saved.getId());
        assertThat(failed).isPresent();
        assertThat(failed.get().getOpStatus()).isEqualTo("FAILED");
        assertThat(failed.get().getErrMsg()).isEqualTo("Connection timeout");
    }

    @Test
    void testUniqueConstraint() {
        // 保存第一个记录
        suiteOffsetRepository.save(testOffset);
        
        // 尝试保存相同键值的记录（应该会更新而不是插入新记录）
        SuiteOffset duplicateOffset = SuiteOffset.builder()
                .suiteId("test_suite_001")
                .deptId("dept_001")
                .hostCode("host_001")
                .dataType("user_data")
                .subType("basic_info")
                .offsetValue(300L)  // 不同的偏移量
                .opStatus("SUCCESS")
                .build();
        
        // 检查是否存在
        boolean exists = suiteOffsetRepository.existsBySuiteIdAndDeptIdAndHostCodeAndDataTypeAndSubType(
                "test_suite_001", "dept_001", "host_001", "user_data", "basic_info");
        
        assertThat(exists).isTrue();
    }
}
