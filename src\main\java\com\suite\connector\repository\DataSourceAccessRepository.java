package com.suite.connector.repository;

import com.suite.connector.model.entity.DataSourceAccess;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 数据源访问记录数据访问接口
 * 
 * <AUTHOR> Connector Team
 */
@Repository
public interface DataSourceAccessRepository extends JpaRepository<DataSourceAccess, Long> {

    /**
     * 根据套件ID查找记录
     */
    Optional<DataSourceAccess> findBySuiteId(String suiteId);

    /**
     * 根据状态查找记录
     */
    List<DataSourceAccess> findByStatus(String status);

    /**
     * 查找所有活跃的套件配置
     */
    @Query("SELECT dsa FROM DataSourceAccess dsa WHERE dsa.params IS NOT NULL AND dsa.params != ''")
    List<DataSourceAccess> findAllActiveSuites();

    /**
     * 查找最近同步的记录
     */
    @Query("SELECT dsa FROM DataSourceAccess dsa WHERE dsa.lastTime >= :since ORDER BY dsa.lastTime DESC")
    List<DataSourceAccess> findRecentSyncRecords(@Param("since") LocalDateTime since);

    /**
     * 查找失败的同步记录
     */
    List<DataSourceAccess> findByStatusAndLastTimeGreaterThanEqual(String status, LocalDateTime since);

    /**
     * 查找长时间未同步的记录
     */
    @Query("SELECT dsa FROM DataSourceAccess dsa WHERE dsa.lastTime < :threshold OR dsa.lastTime IS NULL")
    List<DataSourceAccess> findStaleRecords(@Param("threshold") LocalDateTime threshold);

    /**
     * 根据主题名称查找记录
     */
    Optional<DataSourceAccess> findByTopic(String topic);

    /**
     * 检查套件ID是否存在
     */
    boolean existsBySuiteId(String suiteId);

    /**
     * 更新同步状态
     */
    @Modifying
    @Query("UPDATE DataSourceAccess dsa SET dsa.status = :status, dsa.total = :total, " +
           "dsa.lastTime = :lastTime WHERE dsa.suiteId = :suiteId")
    int updateSyncStatus(@Param("suiteId") String suiteId, 
                        @Param("status") String status, 
                        @Param("total") Long total, 
                        @Param("lastTime") LocalDateTime lastTime);

    /**
     * 更新套件配置参数
     */
    @Modifying
    @Query("UPDATE DataSourceAccess dsa SET dsa.params = :params WHERE dsa.suiteId = :suiteId")
    int updateParams(@Param("suiteId") String suiteId, @Param("params") String params);

    /**
     * 批量更新状态
     */
    @Modifying
    @Query("UPDATE DataSourceAccess dsa SET dsa.status = :status WHERE dsa.suiteId IN :suiteIds")
    int batchUpdateStatus(@Param("suiteIds") List<String> suiteIds, @Param("status") String status);

    /**
     * 获取同步统计信息
     */
    @Query("SELECT dsa.status, COUNT(dsa) FROM DataSourceAccess dsa GROUP BY dsa.status")
    List<Object[]> getSyncStatistics();

    /**
     * 获取总数据量统计
     */
    @Query("SELECT SUM(dsa.total) FROM DataSourceAccess dsa WHERE dsa.status = 'SUCCESS'")
    Long getTotalDataCount();

    /**
     * 查找需要刷新的套件列表
     * 用于/suite/refresh接口
     */
    @Query("SELECT dsa.suiteId, dsa.params FROM DataSourceAccess dsa WHERE dsa.params IS NOT NULL")
    List<Object[]> findSuiteConfigsForRefresh();

    /**
     * 根据套件ID列表查找记录
     */
    List<DataSourceAccess> findBySuiteIdIn(List<String> suiteIds);

    /**
     * 删除套件记录
     */
    void deleteBySuiteId(String suiteId);

    /**
     * 查找处理中状态超时的记录
     */
    @Query("SELECT dsa FROM DataSourceAccess dsa WHERE dsa.status = 'PROCESSING' AND dsa.updatedTime < :timeout")
    List<DataSourceAccess> findTimeoutProcessingRecords(@Param("timeout") LocalDateTime timeout);

    /**
     * 重置超时的处理中记录
     */
    @Modifying
    @Query("UPDATE DataSourceAccess dsa SET dsa.status = 'FAILED' WHERE dsa.status = 'PROCESSING' " +
           "AND dsa.updatedTime < :timeout")
    int resetTimeoutProcessingRecords(@Param("timeout") LocalDateTime timeout);

    /**
     * 获取套件配置的键值对
     * 用于解析params字段
     */
    @Query("SELECT dsa.suiteId, dsa.params FROM DataSourceAccess dsa WHERE dsa.suiteId = :suiteId")
    Optional<Object[]> getSuiteConfig(@Param("suiteId") String suiteId);
}
