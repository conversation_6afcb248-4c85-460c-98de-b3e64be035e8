套件对接流程:

1.根据配置的套件protocol,host,port,请求套件算法接口,获取加密算法
2.根据算法计算请求必须携带的key(需要结合配置的秘钥和UUID进行SM3,SM4计算)
3.请求组织结构接口,获取组织机构数据
4.解析机构数据,获取部门列表
5.根据部门id,请求部门下的终端列表接口,获取终端列表
6.根据dept_id,终端host_code,以及配置的数据类型type以及子类型sub_type(列表),请求每一个sub_type的数量接口,获取子类型下有多少数据
7.根据数量以及配置的page_size计算分页情况,分页请求对应数据接口数据
8.解析数据,将数据写入kafka的suite_{ip}_{port}主题
9.记录该类型数据的拉取进度,及保存offset,first_time,last_time,op_status,err_msg到suite_offset表
10.再执行完一轮之后,将suite_offset的情况,同步到datasource_access表,记录suite_id,topic,total,status,first_time,last_time中,只要这一轮有一个报错,就认为是异常