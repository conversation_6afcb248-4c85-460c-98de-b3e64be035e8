套件对接流程:

1.根据配置的套件protocol,host,port,请求套件算法接口,获取加密算法
2.根据算法计算请求必须携带的key(需要结合配置的秘钥和UUID进行SM3,SM4计算)
3.请求组织结构接口,获取组织机构数据
4.解析机构数据,获取部门列表
5.根据部门id,请求部门下的终端列表接口,获取终端列表
6.根据dept_id,终端host_code,以及配置的数据类型type以及子类型sub_type(列表),请求每一个sub_type的数量接口,获取子类型下有多少数据
7.根据数量以及配置的page_size计算分页情况,分页请求对应数据接口数据
8.解析数据,将数据写入kafka的suite_{ip}_{port}主题
9.记录该类型数据的拉取进度,及保存offset,first_time,last_time,op_status,err_msg到suite_offset表
10.再执行完一轮之后,将suite_offset的情况,同步到 datasource_access 表,记录suite_id,topic,total,status,first_time,last_time中,只要这一轮有一个报错,就认为是异常
11.等待5min,执行下一轮
12.提供一个接口/suite/refresh
当收到refresh请求,从数据库datasource_access获取套件配置信息列表的suite_id,params
解析param,格式: {secret:"xxxxxx",url:"https://127.0.0.1:5443/api"}
解析出套件的host,port,protocol
根据套件列表,重新进行定时拉取任务的执行

其他说明:
1.循环拉取过程中,直接从上一次拉取的地方开始拉取,所以不需要重复请求机构,部门,终端列表等信息,可能是需要缓存一下这些数据,但是refresh的时候,要从头开始
2.接口返回的都是xml格式
3.