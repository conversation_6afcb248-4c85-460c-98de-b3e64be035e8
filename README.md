# Suite Connector - 套件数据同步连接器

## 项目简介

Suite Connector 是一个企业级的套件数据同步服务，用于周期性地从各种套件系统中拉取数据并同步到Kafka消息队列中。

## 核心功能

- 🔄 **周期性数据同步**: 每5分钟自动同步套件数据
- 🔐 **国密算法支持**: 集成SM3/SM4加密算法
- 📊 **增量同步**: 基于offset机制，避免重复数据传输
- 🛡️ **容错机制**: 集成熔断、重试、超时控制
- 📈 **负载均衡**: 支持多实例部署和负载均衡
- 🎯 **动态配置**: 支持运行时刷新套件配置

## 技术栈

- **核心框架**: Spring Boot 2.7.18
- **HTTP客户端**: Spring WebFlux + WebClient
- **负载均衡**: Spring Cloud LoadBalancer
- **容错处理**: Resilience4j
- **数据库**: Spring Data JPA + MySQL
- **消息队列**: Apache Kafka
- **加密算法**: BouncyCastle (国密SM3/SM4)

## 系统要求

- JDK 11 (兼容JDK 8-11)
- MySQL 8.0+
- Apache Kafka 2.8+
- Maven 3.6+

## 快速开始

### 1. 环境准备

```bash
# 启动MySQL
docker run -d --name mysql \
  -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=suite_connector \
  -p 3306:3306 mysql:8.0

# 启动Kafka
docker run -d --name kafka \
  -p 9092:9092 \
  -e KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181 \
  -e KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092 \
  confluentinc/cp-kafka:latest
```

### 2. 配置数据库

修改 `src/main/resources/application.yml` 中的数据库配置：

```yaml
spring:
  datasource:
    url: *******************************************
    username: root
    password: password
```

### 3. 启动应用

```bash
mvn clean install
mvn spring-boot:run
```

### 4. 验证启动

访问健康检查接口：
```bash
curl http://localhost:8080/suite-connector/actuator/health
```

## 项目结构

```
suite-connector/
├── src/main/java/com/suite/connector/
│   ├── SuiteConnectorApplication.java    # 主应用类
│   ├── config/                           # 配置类
│   │   ├── WebClientConfig.java         # WebClient配置
│   │   ├── SchedulerConfig.java         # 定时任务配置
│   │   └── KafkaConfig.java            # Kafka配置
│   ├── model/                           # 数据模型
│   ├── service/                         # 业务服务
│   ├── repository/                      # 数据访问
│   ├── controller/                      # REST控制器
│   ├── client/                          # HTTP客户端
│   └── util/                           # 工具类
├── src/main/resources/
│   ├── application.yml                  # 主配置文件
│   └── application-dev.yml             # 开发环境配置
└── pom.xml                             # Maven配置
```

## 核心配置

### 定时任务配置
```yaml
suite:
  connector:
    scheduler:
      sync-interval: 300000  # 5分钟
      initial-delay: 30000   # 启动延迟30秒
```

### 容错配置
```yaml
resilience4j:
  circuitbreaker:
    instances:
      suite-api:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
```

## API接口

### 刷新套件配置
```bash
POST /suite-connector/api/suite/refresh
```

### 健康检查
```bash
GET /suite-connector/actuator/health
```

## 开发指南

### 添加新的套件类型
1. 在 `model` 包中定义数据模型
2. 在 `client` 包中实现API客户端
3. 在 `service` 包中实现业务逻辑
4. 更新配置文件

### 自定义加密算法
在 `util` 包中实现 `CryptoService` 接口

## 监控和运维

- **健康检查**: `/actuator/health`
- **指标监控**: `/actuator/metrics`
- **Prometheus**: `/actuator/prometheus`
- **日志文件**: `logs/suite-connector.log`

## 许可证

Copyright © 2024 Suite Connector Team. All rights reserved.
