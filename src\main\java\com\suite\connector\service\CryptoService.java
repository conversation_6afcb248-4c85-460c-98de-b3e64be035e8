package com.suite.connector.service;

import com.suite.connector.util.SM3Util;
import com.suite.connector.util.SM4Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 国密算法加密服务
 * 提供SM3哈希和SM4加密的业务封装
 * 
 * <AUTHOR> Connector Team
 */
@Slf4j
@Service
public class CryptoService {

    /**
     * 时间戳格式
     */
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 根据秘钥和UUID计算请求认证key
     * 这是套件API调用的核心认证方法
     * 
     * @param secret 套件配置的秘钥
     * @param uuid 请求唯一标识符
     * @return 计算得到的认证key
     */
    public String calculateKey(String secret, String uuid) {
        if (secret == null || secret.isEmpty()) {
            throw new IllegalArgumentException("秘钥不能为空");
        }
        
        if (uuid == null || uuid.isEmpty()) {
            throw new IllegalArgumentException("UUID不能为空");
        }

        try {
            // 步骤1: 使用SM3计算秘钥和UUID的哈希
            String combined = secret + uuid;
            String sm3Hash = SM3Util.hash(combined);
            
            log.debug("SM3哈希计算: secret={}, uuid={}, hash={}", 
                     maskSecret(secret), uuid, sm3Hash);
            
            // 步骤2: 使用SM4加密哈希值
            String sm4Key = SM4Util.generateKeyFromPassword(secret);
            String encryptedKey = SM4Util.encryptWithFixedIV(sm3Hash, sm4Key);
            
            log.debug("SM4加密完成: key长度={}", encryptedKey.length());
            
            return encryptedKey;
            
        } catch (Exception e) {
            log.error("计算认证key失败: secret={}, uuid={}", maskSecret(secret), uuid, e);
            throw new RuntimeException("计算认证key失败", e);
        }
    }

    /**
     * 计算带时间戳的认证key
     * 增加时间戳可以防止重放攻击
     * 
     * @param secret 套件配置的秘钥
     * @param uuid 请求唯一标识符
     * @param timestamp 时间戳
     * @return 计算得到的认证key
     */
    public String calculateKeyWithTimestamp(String secret, String uuid, LocalDateTime timestamp) {
        String timestampStr = timestamp.format(TIMESTAMP_FORMAT);
        String combined = secret + uuid + timestampStr;
        
        // 使用SM3计算哈希
        String sm3Hash = SM3Util.hash(combined);
        
        // 使用SM4加密
        String sm4Key = SM4Util.generateKeyFromPassword(secret);
        return SM4Util.encryptWithFixedIV(sm3Hash, sm4Key);
    }

    /**
     * 计算当前时间戳的认证key
     * 
     * @param secret 套件配置的秘钥
     * @param uuid 请求唯一标识符
     * @return 计算得到的认证key
     */
    public String calculateKeyWithCurrentTimestamp(String secret, String uuid) {
        return calculateKeyWithTimestamp(secret, uuid, LocalDateTime.now());
    }

    /**
     * 验证认证key是否正确
     * 
     * @param secret 套件配置的秘钥
     * @param uuid 请求唯一标识符
     * @param expectedKey 期望的认证key
     * @return 是否验证通过
     */
    public boolean verifyKey(String secret, String uuid, String expectedKey) {
        try {
            String calculatedKey = calculateKey(secret, uuid);
            return calculatedKey.equals(expectedKey);
        } catch (Exception e) {
            log.error("验证认证key失败", e);
            return false;
        }
    }

    /**
     * 生成新的UUID
     * 
     * @return UUID字符串
     */
    public String generateUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成不带连字符的UUID
     * 
     * @return 不带连字符的UUID字符串
     */
    public String generateSimpleUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 使用SM3计算数据完整性校验值
     * 
     * @param data 需要校验的数据
     * @return SM3哈希值
     */
    public String calculateChecksum(String data) {
        if (data == null) {
            throw new IllegalArgumentException("数据不能为null");
        }
        
        return SM3Util.hash(data);
    }

    /**
     * 验证数据完整性
     * 
     * @param data 原始数据
     * @param expectedChecksum 期望的校验值
     * @return 是否验证通过
     */
    public boolean verifyChecksum(String data, String expectedChecksum) {
        try {
            String actualChecksum = calculateChecksum(data);
            return actualChecksum.equalsIgnoreCase(expectedChecksum);
        } catch (Exception e) {
            log.error("验证数据完整性失败", e);
            return false;
        }
    }

    /**
     * 使用SM4加密敏感数据
     * 
     * @param plaintext 明文
     * @param password 密码
     * @return 加密后的密文
     */
    public String encryptSensitiveData(String plaintext, String password) {
        if (plaintext == null || password == null) {
            throw new IllegalArgumentException("明文和密码都不能为null");
        }
        
        try {
            String key = SM4Util.generateKeyFromPassword(password);
            return SM4Util.encryptWithFixedIV(plaintext, key);
        } catch (Exception e) {
            log.error("加密敏感数据失败", e);
            throw new RuntimeException("加密敏感数据失败", e);
        }
    }

    /**
     * 使用SM4解密敏感数据
     * 
     * @param ciphertext 密文
     * @param password 密码
     * @return 解密后的明文
     */
    public String decryptSensitiveData(String ciphertext, String password) {
        if (ciphertext == null || password == null) {
            throw new IllegalArgumentException("密文和密码都不能为null");
        }
        
        try {
            String key = SM4Util.generateKeyFromPassword(password);
            return SM4Util.decryptWithFixedIV(ciphertext, key);
        } catch (Exception e) {
            log.error("解密敏感数据失败", e);
            throw new RuntimeException("解密敏感数据失败", e);
        }
    }

    /**
     * 生成HMAC签名
     * 
     * @param data 需要签名的数据
     * @param secret 签名密钥
     * @return HMAC-SM3签名
     */
    public String generateSignature(String data, String secret) {
        if (data == null || secret == null) {
            throw new IllegalArgumentException("数据和密钥都不能为null");
        }
        
        return SM3Util.hmac(secret, data);
    }

    /**
     * 验证HMAC签名
     * 
     * @param data 原始数据
     * @param secret 签名密钥
     * @param expectedSignature 期望的签名
     * @return 是否验证通过
     */
    public boolean verifySignature(String data, String secret, String expectedSignature) {
        try {
            String actualSignature = generateSignature(data, secret);
            return actualSignature.equalsIgnoreCase(expectedSignature);
        } catch (Exception e) {
            log.error("验证HMAC签名失败", e);
            return false;
        }
    }

    /**
     * 掩码显示秘钥（用于日志记录）
     * 
     * @param secret 原始秘钥
     * @return 掩码后的秘钥
     */
    private String maskSecret(String secret) {
        if (secret == null || secret.length() <= 4) {
            return "***";
        }
        
        return secret.substring(0, 2) + "***" + secret.substring(secret.length() - 2);
    }

    /**
     * 生成随机密钥
     * 
     * @return 随机SM4密钥
     */
    public String generateRandomKey() {
        return SM4Util.generateKey();
    }

    /**
     * 生成随机盐值
     * 
     * @return 随机盐值
     */
    public String generateRandomSalt() {
        return SM3Util.generateSalt();
    }
}
