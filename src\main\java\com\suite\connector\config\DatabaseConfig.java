package com.suite.connector.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 数据库配置类
 * 
 * <AUTHOR> Connector Team
 */
@Slf4j
@Configuration
@EnableJpaRepositories(basePackages = "com.suite.connector.repository")
@EnableTransactionManagement
public class DatabaseConfig {

    @Value("${spring.datasource.url}")
    private String datasourceUrl;

    @Value("${spring.jpa.hibernate.ddl-auto:update}")
    private String ddlAuto;

    /**
     * 配置JPA事务管理器
     */
    @Bean
    public PlatformTransactionManager transactionManager(EntityManagerFactory entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory);
        
        log.info("JPA Transaction Manager configured for database: {}", 
                 datasourceUrl.replaceAll("password=[^&]*", "password=***"));
        
        return transactionManager;
    }

    /**
     * 数据库初始化完成后的回调
     */
    @Bean
    public DatabaseInitializer databaseInitializer(DataSource dataSource) {
        return new DatabaseInitializer(dataSource, ddlAuto);
    }

    /**
     * 数据库初始化器
     */
    public static class DatabaseInitializer {
        private final DataSource dataSource;
        private final String ddlAuto;

        public DatabaseInitializer(DataSource dataSource, String ddlAuto) {
            this.dataSource = dataSource;
            this.ddlAuto = ddlAuto;
            
            log.info("Database initialized with DDL auto: {}", ddlAuto);
            
            // 在开发环境下可以执行一些初始化脚本
            if ("create-drop".equals(ddlAuto) || "create".equals(ddlAuto)) {
                log.info("Database will be recreated on startup");
            }
        }

        public DataSource getDataSource() {
            return dataSource;
        }
    }
}
