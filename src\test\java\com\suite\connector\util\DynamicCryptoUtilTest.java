package com.suite.connector.util;

import com.suite.connector.model.dto.AlgorithmConfigDTO;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * 动态加密工具类测试
 * 
 * <AUTHOR> Connector Team
 */
class DynamicCryptoUtilTest {

    @Test
    void testEncryptDecryptWithECBMode() {
        // 测试ECB模式加密解密
        String plaintext = "Hello, Dynamic Crypto!";
        String key = SM4Util.generateKey();
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .paramNum(3)
                .params("ECB,PKCS7,0000000000000000")
                .build();
        config.parseParams();
        
        // 加密
        String ciphertext = DynamicCryptoUtil.encrypt(plaintext, key, config);
        assertThat(ciphertext).isNotNull();
        assertThat(ciphertext).isNotEqualTo(plaintext);
        
        // 解密
        String decrypted = DynamicCryptoUtil.decrypt(ciphertext, key, config);
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptDecryptWithCBCMode() {
        // 测试CBC模式加密解密
        String plaintext = "Hello, CBC Mode!";
        String key = SM4Util.generateKey();
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .paramNum(3)
                .params("CBC,PKCS7,1234567890abcdef1234567890abcdef")
                .build();
        config.parseParams();
        
        // 加密
        String ciphertext = DynamicCryptoUtil.encrypt(plaintext, key, config);
        assertThat(ciphertext).isNotNull();
        assertThat(ciphertext).isNotEqualTo(plaintext);
        
        // 解密
        String decrypted = DynamicCryptoUtil.decrypt(ciphertext, key, config);
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptDecryptWithDefaultIV() {
        // 测试使用默认IV的CBC模式
        String plaintext = "Test with default IV";
        String key = SM4Util.generateKey();
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .paramNum(3)
                .params("CBC,PKCS7,0000000000000000") // 全零IV
                .build();
        config.parseParams();
        
        // 加密
        String ciphertext = DynamicCryptoUtil.encrypt(plaintext, key, config);
        assertThat(ciphertext).isNotNull();
        
        // 解密
        String decrypted = DynamicCryptoUtil.decrypt(ciphertext, key, config);
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptConsistency() {
        // 测试相同输入的加密一致性
        String plaintext = "Consistency test";
        String key = "1234567890abcdef1234567890abcdef"; // 固定密钥
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .paramNum(3)
                .params("ECB,PKCS7,0000000000000000")
                .build();
        config.parseParams();
        
        String ciphertext1 = DynamicCryptoUtil.encrypt(plaintext, key, config);
        String ciphertext2 = DynamicCryptoUtil.encrypt(plaintext, key, config);
        
        assertThat(ciphertext1).isEqualTo(ciphertext2);
    }

    @Test
    void testEncryptWithDifferentKeys() {
        // 测试不同密钥产生不同密文
        String plaintext = "Same plaintext";
        String key1 = SM4Util.generateKey();
        String key2 = SM4Util.generateKey();
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .paramNum(3)
                .params("ECB,PKCS7,0000000000000000")
                .build();
        config.parseParams();
        
        String ciphertext1 = DynamicCryptoUtil.encrypt(plaintext, key1, config);
        String ciphertext2 = DynamicCryptoUtil.encrypt(plaintext, key2, config);
        
        assertThat(ciphertext1).isNotEqualTo(ciphertext2);
    }

    @Test
    void testEncryptWithNullInputs() {
        // 测试null输入
        AlgorithmConfigDTO config = AlgorithmConfigDTO.createDefaultSM4();
        String key = SM4Util.generateKey();
        
        assertThatThrownBy(() -> DynamicCryptoUtil.encrypt(null, key, config))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("参数不能为null");
        
        assertThatThrownBy(() -> DynamicCryptoUtil.encrypt("test", null, config))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("参数不能为null");
        
        assertThatThrownBy(() -> DynamicCryptoUtil.encrypt("test", key, null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("参数不能为null");
    }

    @Test
    void testEncryptWithInvalidConfig() {
        // 测试无效配置
        String plaintext = "test";
        String key = SM4Util.generateKey();
        
        AlgorithmConfigDTO invalidConfig = new AlgorithmConfigDTO();
        // 不设置任何参数，配置无效
        
        assertThatThrownBy(() -> DynamicCryptoUtil.encrypt(plaintext, key, invalidConfig))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("无效的算法配置");
    }

    @Test
    void testEncryptEmptyString() {
        // 测试加密空字符串
        String plaintext = "";
        String key = SM4Util.generateKey();
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.createDefaultSM4();
        
        String ciphertext = DynamicCryptoUtil.encrypt(plaintext, key, config);
        String decrypted = DynamicCryptoUtil.decrypt(ciphertext, key, config);
        
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptLongText() {
        // 测试加密长文本
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("This is a long text for testing dynamic encryption. ");
        }
        String plaintext = sb.toString();
        
        String key = SM4Util.generateKey();
        AlgorithmConfigDTO config = AlgorithmConfigDTO.createDefaultSM4();
        
        String ciphertext = DynamicCryptoUtil.encrypt(plaintext, key, config);
        String decrypted = DynamicCryptoUtil.decrypt(ciphertext, key, config);
        
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptSpecialCharacters() {
        // 测试加密特殊字符
        String plaintext = "特殊字符测试: !@#$%^&*()_+-=[]{}|;':\",./<>?`~";
        String key = SM4Util.generateKey();
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.createDefaultSM4();
        
        String ciphertext = DynamicCryptoUtil.encrypt(plaintext, key, config);
        String decrypted = DynamicCryptoUtil.decrypt(ciphertext, key, config);
        
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testIsValidKeyLength() {
        // 测试密钥长度验证
        byte[] validKey = new byte[16];
        byte[] invalidKey = new byte[8];
        
        assertThat(DynamicCryptoUtil.isValidKeyLength("SM4", validKey)).isTrue();
        assertThat(DynamicCryptoUtil.isValidKeyLength("SM4", invalidKey)).isFalse();
        assertThat(DynamicCryptoUtil.isValidKeyLength("SM4", null)).isFalse();
        assertThat(DynamicCryptoUtil.isValidKeyLength(null, validKey)).isFalse();
    }

    @Test
    void testIsValidIVLength() {
        // 测试IV长度验证
        byte[] validIV = new byte[16];
        byte[] invalidIV = new byte[8];
        
        assertThat(DynamicCryptoUtil.isValidIVLength("SM4", validIV)).isTrue();
        assertThat(DynamicCryptoUtil.isValidIVLength("SM4", invalidIV)).isFalse();
        assertThat(DynamicCryptoUtil.isValidIVLength("SM4", null)).isFalse();
        assertThat(DynamicCryptoUtil.isValidIVLength(null, validIV)).isFalse();
    }

    @Test
    void testGetDefaultKeyLength() {
        // 测试获取默认密钥长度
        assertThat(DynamicCryptoUtil.getDefaultKeyLength("SM4")).isEqualTo(16);
        assertThat(DynamicCryptoUtil.getDefaultKeyLength("UNKNOWN")).isEqualTo(0);
        assertThat(DynamicCryptoUtil.getDefaultKeyLength(null)).isEqualTo(0);
    }

    @Test
    void testGetDefaultIVLength() {
        // 测试获取默认IV长度
        assertThat(DynamicCryptoUtil.getDefaultIVLength("SM4")).isEqualTo(16);
        assertThat(DynamicCryptoUtil.getDefaultIVLength("UNKNOWN")).isEqualTo(0);
        assertThat(DynamicCryptoUtil.getDefaultIVLength(null)).isEqualTo(0);
    }

    @Test
    void testRealWorldScenario() {
        // 测试真实场景：解析XML配置并进行加密
        String xmlConfig = "<Root>\n" +
                          "\t<Algorithm>SM4</Algorithm>\n" +
                          "\t<ParamNum>3</ParamNum>\n" +
                          "\t<Params>CBC,PKCS#5,1234567890abcdef1234567890abcdef</Params>\n" +
                          "</Root>";
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.fromXml(xmlConfig);
        
        String plaintext = "Real world data to encrypt";
        String key = SM4Util.generateKey();
        
        // 加密
        String ciphertext = DynamicCryptoUtil.encrypt(plaintext, key, config);
        assertThat(ciphertext).isNotNull();
        assertThat(ciphertext).isNotEqualTo(plaintext);
        
        // 解密
        String decrypted = DynamicCryptoUtil.decrypt(ciphertext, key, config);
        assertThat(decrypted).isEqualTo(plaintext);
        
        // 验证配置
        assertThat(config.getAlgorithm()).isEqualTo("SM4");
        assertThat(config.getNormalizedMode()).isEqualTo("CBC");
        assertThat(config.getNormalizedPadding()).isEqualTo("PKCS7");
        assertThat(config.requiresIV()).isTrue();
    }
}
