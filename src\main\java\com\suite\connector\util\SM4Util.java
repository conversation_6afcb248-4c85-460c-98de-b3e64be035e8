package com.suite.connector.util;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.modes.CBCBlockCipher;
import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
import org.bouncycastle.crypto.paddings.PKCS7Padding;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * SM4对称加密算法工具类
 * SM4是中国国家密码管理局发布的分组密码标准
 * 
 * <AUTHOR> Connector Team
 */
@Slf4j
public class SM4Util {

    /**
     * SM4密钥长度（字节）
     */
    public static final int KEY_LENGTH = 16;

    /**
     * SM4初始化向量长度（字节）
     */
    public static final int IV_LENGTH = 16;

    /**
     * SM4分组长度（字节）
     */
    public static final int BLOCK_SIZE = 16;

    /**
     * 生成随机密钥
     * 
     * @return 16字节的随机密钥（十六进制字符串）
     */
    public static String generateKey() {
        SecureRandom random = new SecureRandom();
        byte[] keyBytes = new byte[KEY_LENGTH];
        random.nextBytes(keyBytes);
        return Hex.toHexString(keyBytes).toLowerCase();
    }

    /**
     * 生成随机初始化向量
     * 
     * @return 16字节的随机IV（十六进制字符串）
     */
    public static String generateIV() {
        SecureRandom random = new SecureRandom();
        byte[] ivBytes = new byte[IV_LENGTH];
        random.nextBytes(ivBytes);
        return Hex.toHexString(ivBytes).toLowerCase();
    }

    /**
     * SM4-CBC加密（使用PKCS7填充）
     * 
     * @param plaintext 明文字符串
     * @param key 密钥（十六进制字符串，32字符）
     * @param iv 初始化向量（十六进制字符串，32字符）
     * @return 加密后的密文（Base64编码）
     */
    public static String encrypt(String plaintext, String key, String iv) {
        if (plaintext == null || key == null || iv == null) {
            throw new IllegalArgumentException("明文、密钥和IV都不能为null");
        }
        
        return encrypt(plaintext.getBytes(StandardCharsets.UTF_8), 
                      Hex.decode(key), 
                      Hex.decode(iv));
    }

    /**
     * SM4-CBC加密（使用PKCS7填充）
     * 
     * @param plaintext 明文字节数组
     * @param key 密钥字节数组（16字节）
     * @param iv 初始化向量字节数组（16字节）
     * @return 加密后的密文（Base64编码）
     */
    public static String encrypt(byte[] plaintext, byte[] key, byte[] iv) {
        validateKeyAndIV(key, iv);
        
        try {
            // 创建SM4引擎和CBC模式
            SM4Engine engine = new SM4Engine();
            CBCBlockCipher cbcCipher = new CBCBlockCipher(engine);
            PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(cbcCipher, new PKCS7Padding());
            
            // 初始化加密参数
            KeyParameter keyParam = new KeyParameter(key);
            ParametersWithIV params = new ParametersWithIV(keyParam, iv);
            cipher.init(true, params);
            
            // 执行加密
            byte[] ciphertext = new byte[cipher.getOutputSize(plaintext.length)];
            int len = cipher.processBytes(plaintext, 0, plaintext.length, ciphertext, 0);
            len += cipher.doFinal(ciphertext, len);
            
            // 截取实际长度的密文
            byte[] result = new byte[len];
            System.arraycopy(ciphertext, 0, result, 0, len);
            
            return Base64.toBase64String(result);
            
        } catch (Exception e) {
            log.error("SM4加密失败", e);
            throw new RuntimeException("SM4加密失败", e);
        }
    }

    /**
     * SM4-CBC解密（使用PKCS7填充）
     * 
     * @param ciphertext 密文（Base64编码）
     * @param key 密钥（十六进制字符串，32字符）
     * @param iv 初始化向量（十六进制字符串，32字符）
     * @return 解密后的明文字符串
     */
    public static String decrypt(String ciphertext, String key, String iv) {
        if (ciphertext == null || key == null || iv == null) {
            throw new IllegalArgumentException("密文、密钥和IV都不能为null");
        }
        
        byte[] decrypted = decrypt(Base64.decode(ciphertext), 
                                 Hex.decode(key), 
                                 Hex.decode(iv));
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * SM4-CBC解密（使用PKCS7填充）
     * 
     * @param ciphertext 密文字节数组
     * @param key 密钥字节数组（16字节）
     * @param iv 初始化向量字节数组（16字节）
     * @return 解密后的明文字节数组
     */
    public static byte[] decrypt(byte[] ciphertext, byte[] key, byte[] iv) {
        validateKeyAndIV(key, iv);
        
        try {
            // 创建SM4引擎和CBC模式
            SM4Engine engine = new SM4Engine();
            CBCBlockCipher cbcCipher = new CBCBlockCipher(engine);
            PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(cbcCipher, new PKCS7Padding());
            
            // 初始化解密参数
            KeyParameter keyParam = new KeyParameter(key);
            ParametersWithIV params = new ParametersWithIV(keyParam, iv);
            cipher.init(false, params);
            
            // 执行解密
            byte[] plaintext = new byte[cipher.getOutputSize(ciphertext.length)];
            int len = cipher.processBytes(ciphertext, 0, ciphertext.length, plaintext, 0);
            len += cipher.doFinal(plaintext, len);
            
            // 截取实际长度的明文
            byte[] result = new byte[len];
            System.arraycopy(plaintext, 0, result, 0, len);
            
            return result;
            
        } catch (Exception e) {
            log.error("SM4解密失败", e);
            throw new RuntimeException("SM4解密失败", e);
        }
    }

    /**
     * 使用固定IV的简化加密方法
     * 注意：生产环境中不建议使用固定IV
     * 
     * @param plaintext 明文字符串
     * @param key 密钥（十六进制字符串）
     * @return 加密后的密文（Base64编码）
     */
    public static String encryptWithFixedIV(String plaintext, String key) {
        // 使用密钥的前16字节作为IV（仅用于演示，生产环境不推荐）
        String iv = key.length() >= 32 ? key.substring(0, 32) : key + "0".repeat(32 - key.length());
        return encrypt(plaintext, key, iv);
    }

    /**
     * 使用固定IV的简化解密方法
     * 
     * @param ciphertext 密文（Base64编码）
     * @param key 密钥（十六进制字符串）
     * @return 解密后的明文字符串
     */
    public static String decryptWithFixedIV(String ciphertext, String key) {
        // 使用密钥的前16字节作为IV（仅用于演示，生产环境不推荐）
        String iv = key.length() >= 32 ? key.substring(0, 32) : key + "0".repeat(32 - key.length());
        return decrypt(ciphertext, key, iv);
    }

    /**
     * 从密码字符串生成SM4密钥
     * 使用SM3哈希算法将任意长度的密码转换为16字节密钥
     * 
     * @param password 密码字符串
     * @return SM4密钥（十六进制字符串，32字符）
     */
    public static String generateKeyFromPassword(String password) {
        if (password == null || password.isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        
        // 使用SM3哈希生成密钥，取前16字节
        String hash = SM3Util.hash(password);
        return hash.substring(0, 32); // 32个十六进制字符 = 16字节
    }

    /**
     * 验证密钥格式
     * 
     * @param key 密钥（十六进制字符串）
     * @return 是否为有效的SM4密钥
     */
    public static boolean isValidKey(String key) {
        if (key == null) {
            return false;
        }
        
        // 检查长度（32个十六进制字符 = 16字节）
        if (key.length() != 32) {
            return false;
        }
        
        // 检查是否为十六进制字符串
        return key.matches("^[0-9a-fA-F]+$");
    }

    /**
     * 验证初始化向量格式
     * 
     * @param iv 初始化向量（十六进制字符串）
     * @return 是否为有效的SM4初始化向量
     */
    public static boolean isValidIV(String iv) {
        if (iv == null) {
            return false;
        }
        
        // 检查长度（32个十六进制字符 = 16字节）
        if (iv.length() != 32) {
            return false;
        }
        
        // 检查是否为十六进制字符串
        return iv.matches("^[0-9a-fA-F]+$");
    }

    /**
     * 验证密钥和IV的有效性
     */
    private static void validateKeyAndIV(byte[] key, byte[] iv) {
        if (key == null || key.length != KEY_LENGTH) {
            throw new IllegalArgumentException("密钥必须为16字节");
        }
        
        if (iv == null || iv.length != IV_LENGTH) {
            throw new IllegalArgumentException("初始化向量必须为16字节");
        }
    }
}
