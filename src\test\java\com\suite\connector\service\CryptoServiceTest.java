package com.suite.connector.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * 加密服务测试类
 * 
 * <AUTHOR> Connector Team
 */
@SpringBootTest
@ActiveProfiles("test")
class CryptoServiceTest {

    private CryptoService cryptoService;

    @BeforeEach
    void setUp() {
        cryptoService = new CryptoService();
    }

    @Test
    void testCalculateKey() {
        // 测试计算认证key
        String secret = "test_secret_key";
        String uuid = UUID.randomUUID().toString();
        
        String key1 = cryptoService.calculateKey(secret, uuid);
        String key2 = cryptoService.calculateKey(secret, uuid);
        
        assertThat(key1).isNotNull();
        assertThat(key1).isNotEmpty();
        assertThat(key1).isEqualTo(key2); // 相同输入应该产生相同结果
    }

    @Test
    void testCalculateKeyWithDifferentInputs() {
        // 测试不同输入产生不同key
        String secret1 = "secret1";
        String secret2 = "secret2";
        String uuid = UUID.randomUUID().toString();
        
        String key1 = cryptoService.calculateKey(secret1, uuid);
        String key2 = cryptoService.calculateKey(secret2, uuid);
        
        assertThat(key1).isNotEqualTo(key2);
    }

    @Test
    void testCalculateKeyWithNullInputs() {
        // 测试null输入
        String uuid = UUID.randomUUID().toString();
        
        assertThatThrownBy(() -> cryptoService.calculateKey(null, uuid))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("秘钥不能为空");
        
        assertThatThrownBy(() -> cryptoService.calculateKey("secret", null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("UUID不能为空");
    }

    @Test
    void testCalculateKeyWithEmptyInputs() {
        // 测试空字符串输入
        String uuid = UUID.randomUUID().toString();
        
        assertThatThrownBy(() -> cryptoService.calculateKey("", uuid))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("秘钥不能为空");
        
        assertThatThrownBy(() -> cryptoService.calculateKey("secret", ""))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("UUID不能为空");
    }

    @Test
    void testCalculateKeyWithTimestamp() {
        // 测试带时间戳的key计算
        String secret = "test_secret";
        String uuid = UUID.randomUUID().toString();
        LocalDateTime timestamp = LocalDateTime.now();
        
        String key1 = cryptoService.calculateKeyWithTimestamp(secret, uuid, timestamp);
        String key2 = cryptoService.calculateKeyWithTimestamp(secret, uuid, timestamp);
        
        assertThat(key1).isNotNull();
        assertThat(key1).isEqualTo(key2); // 相同时间戳应该产生相同结果
        
        // 不同时间戳应该产生不同结果
        LocalDateTime differentTimestamp = timestamp.plusMinutes(1);
        String key3 = cryptoService.calculateKeyWithTimestamp(secret, uuid, differentTimestamp);
        assertThat(key1).isNotEqualTo(key3);
    }

    @Test
    void testCalculateKeyWithCurrentTimestamp() {
        // 测试当前时间戳key计算
        String secret = "test_secret";
        String uuid = UUID.randomUUID().toString();
        
        String key = cryptoService.calculateKeyWithCurrentTimestamp(secret, uuid);
        
        assertThat(key).isNotNull();
        assertThat(key).isNotEmpty();
    }

    @Test
    void testVerifyKey() {
        // 测试key验证
        String secret = "test_secret";
        String uuid = UUID.randomUUID().toString();
        
        String key = cryptoService.calculateKey(secret, uuid);
        
        assertThat(cryptoService.verifyKey(secret, uuid, key)).isTrue();
        assertThat(cryptoService.verifyKey(secret, uuid, "wrong_key")).isFalse();
        assertThat(cryptoService.verifyKey("wrong_secret", uuid, key)).isFalse();
    }

    @Test
    void testGenerateUUID() {
        // 测试UUID生成
        String uuid1 = cryptoService.generateUUID();
        String uuid2 = cryptoService.generateUUID();
        
        assertThat(uuid1).isNotNull();
        assertThat(uuid2).isNotNull();
        assertThat(uuid1).isNotEqualTo(uuid2);
        assertThat(uuid1).matches("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    }

    @Test
    void testGenerateSimpleUUID() {
        // 测试简单UUID生成（无连字符）
        String uuid1 = cryptoService.generateSimpleUUID();
        String uuid2 = cryptoService.generateSimpleUUID();
        
        assertThat(uuid1).isNotNull();
        assertThat(uuid2).isNotNull();
        assertThat(uuid1).isNotEqualTo(uuid2);
        assertThat(uuid1).hasSize(32);
        assertThat(uuid1).matches("^[0-9a-fA-F]{32}$");
    }

    @Test
    void testCalculateChecksum() {
        // 测试数据校验和计算
        String data = "Test data for checksum";
        String checksum1 = cryptoService.calculateChecksum(data);
        String checksum2 = cryptoService.calculateChecksum(data);
        
        assertThat(checksum1).isNotNull();
        assertThat(checksum1).isEqualTo(checksum2); // 相同数据应该产生相同校验和
        assertThat(checksum1).hasSize(64); // SM3哈希长度
    }

    @Test
    void testVerifyChecksum() {
        // 测试校验和验证
        String data = "Test data";
        String checksum = cryptoService.calculateChecksum(data);
        
        assertThat(cryptoService.verifyChecksum(data, checksum)).isTrue();
        assertThat(cryptoService.verifyChecksum(data, "wrong_checksum")).isFalse();
        assertThat(cryptoService.verifyChecksum("wrong_data", checksum)).isFalse();
    }

    @Test
    void testEncryptDecryptSensitiveData() {
        // 测试敏感数据加密解密
        String plaintext = "Sensitive information";
        String password = "encryption_password";
        
        String ciphertext = cryptoService.encryptSensitiveData(plaintext, password);
        String decrypted = cryptoService.decryptSensitiveData(ciphertext, password);
        
        assertThat(ciphertext).isNotNull();
        assertThat(ciphertext).isNotEqualTo(plaintext);
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testGenerateSignature() {
        // 测试签名生成
        String data = "Data to be signed";
        String secret = "signing_secret";
        
        String signature1 = cryptoService.generateSignature(data, secret);
        String signature2 = cryptoService.generateSignature(data, secret);
        
        assertThat(signature1).isNotNull();
        assertThat(signature1).isEqualTo(signature2); // 相同输入应该产生相同签名
        assertThat(signature1).hasSize(64); // HMAC-SM3长度
    }

    @Test
    void testVerifySignature() {
        // 测试签名验证
        String data = "Data to verify";
        String secret = "signing_secret";
        String signature = cryptoService.generateSignature(data, secret);
        
        assertThat(cryptoService.verifySignature(data, secret, signature)).isTrue();
        assertThat(cryptoService.verifySignature(data, secret, "wrong_signature")).isFalse();
        assertThat(cryptoService.verifySignature("wrong_data", secret, signature)).isFalse();
        assertThat(cryptoService.verifySignature(data, "wrong_secret", signature)).isFalse();
    }

    @Test
    void testGenerateRandomKey() {
        // 测试随机密钥生成
        String key1 = cryptoService.generateRandomKey();
        String key2 = cryptoService.generateRandomKey();
        
        assertThat(key1).isNotNull();
        assertThat(key2).isNotNull();
        assertThat(key1).isNotEqualTo(key2);
        assertThat(key1).hasSize(32); // SM4密钥长度
    }

    @Test
    void testGenerateRandomSalt() {
        // 测试随机盐值生成
        String salt1 = cryptoService.generateRandomSalt();
        String salt2 = cryptoService.generateRandomSalt();
        
        assertThat(salt1).isNotNull();
        assertThat(salt2).isNotNull();
        assertThat(salt1).isNotEqualTo(salt2);
        assertThat(salt1).hasSize(32); // 默认盐值长度
    }

    @Test
    void testRealWorldScenario() {
        // 测试真实场景：套件API调用认证
        String suiteSecret = "suite_secret_12345";
        String requestUuid = cryptoService.generateUUID();
        
        // 计算认证key
        String authKey = cryptoService.calculateKey(suiteSecret, requestUuid);
        
        // 验证key
        boolean isValid = cryptoService.verifyKey(suiteSecret, requestUuid, authKey);
        
        assertThat(authKey).isNotNull();
        assertThat(isValid).isTrue();
        
        // 模拟数据完整性校验
        String responseData = "{\"status\":\"success\",\"data\":[]}";
        String checksum = cryptoService.calculateChecksum(responseData);
        boolean checksumValid = cryptoService.verifyChecksum(responseData, checksum);
        
        assertThat(checksumValid).isTrue();
    }
}
