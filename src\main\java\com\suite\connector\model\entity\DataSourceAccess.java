package com.suite.connector.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 数据源访问记录实体
 * 记录套件的整体同步状态和配置信息
 * 
 * <AUTHOR> Connector Team
 */
@Entity
@Table(name = "datasource_access",
       uniqueConstraints = @UniqueConstraint(
           name = "uk_suite_id",
           columnNames = {"suite_id"}
       ))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSourceAccess {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 套件ID，唯一标识
     */
    @Column(name = "suite_id", nullable = false, length = 64, unique = true)
    private String suiteId;

    /**
     * Kafka主题名称
     */
    @Column(name = "topic", nullable = false, length = 128)
    private String topic;

    /**
     * 总数据量
     */
    @Column(name = "total")
    @Builder.Default
    private Long total = 0L;

    /**
     * 同步状态：SUCCESS, FAILED, PROCESSING
     */
    @Column(name = "status", length = 16)
    @Builder.Default
    private String status = "SUCCESS";

    /**
     * 首次同步时间
     */
    @Column(name = "first_time")
    private LocalDateTime firstTime;

    /**
     * 最后同步时间
     */
    @Column(name = "last_time")
    private LocalDateTime lastTime;

    /**
     * 套件配置参数，JSON格式
     * 格式: {"secret":"xxxxxx","url":"https://127.0.0.1:5443/api"}
     */
    @Column(name = "params", columnDefinition = "TEXT")
    private String params;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        SUCCESS("SUCCESS"),
        FAILED("FAILED"),
        PROCESSING("PROCESSING");

        private final String value;

        SyncStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 更新同步状态
     */
    public void updateSyncStatus(String newStatus, Long totalCount, LocalDateTime syncTime) {
        this.status = newStatus;
        this.total = totalCount;
        this.lastTime = syncTime;
        
        // 如果是首次同步，设置首次时间
        if (this.firstTime == null) {
            this.firstTime = syncTime;
        }
    }

    /**
     * 标记为处理中状态
     */
    public void markAsProcessing() {
        this.status = SyncStatus.PROCESSING.getValue();
    }

    /**
     * 标记为成功状态
     */
    public void markAsSuccess(Long totalCount) {
        updateSyncStatus(SyncStatus.SUCCESS.getValue(), totalCount, LocalDateTime.now());
    }

    /**
     * 标记为失败状态
     */
    public void markAsFailed() {
        this.status = SyncStatus.FAILED.getValue();
        this.lastTime = LocalDateTime.now();
    }

    /**
     * 生成Kafka主题名称
     * 格式: suite_{ip}_{port}
     */
    public static String generateTopicName(String ip, String port) {
        return String.format("suite_%s_%s", ip.replace(".", "_"), port);
    }

    /**
     * 从URL解析主题名称
     */
    public String generateTopicFromParams() {
        if (this.params == null || this.params.isEmpty()) {
            return "suite_default";
        }
        
        try {
            // 简单解析URL中的host和port
            // 实际项目中建议使用JSON解析库
            String url = extractUrlFromParams(this.params);
            if (url != null) {
                // 解析URL获取host和port
                java.net.URL parsedUrl = new java.net.URL(url);
                String host = parsedUrl.getHost();
                int port = parsedUrl.getPort();
                if (port == -1) {
                    port = parsedUrl.getDefaultPort();
                }
                return generateTopicName(host, String.valueOf(port));
            }
        } catch (Exception e) {
            // 解析失败时使用默认主题
        }
        
        return "suite_" + this.suiteId;
    }

    /**
     * 从params中提取URL
     * 简单实现，实际项目中建议使用JSON解析
     */
    private String extractUrlFromParams(String params) {
        if (params.contains("\"url\":")) {
            int start = params.indexOf("\"url\":\"") + 7;
            int end = params.indexOf("\"", start);
            if (start > 6 && end > start) {
                return params.substring(start, end);
            }
        }
        return null;
    }
}
