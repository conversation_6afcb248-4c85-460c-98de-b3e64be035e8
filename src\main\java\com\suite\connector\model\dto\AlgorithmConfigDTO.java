package com.suite.connector.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 算法配置数据传输对象
 * 用于解析套件返回的算法配置XML
 * 
 * <AUTHOR> Connector Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmConfigDTO {

    /**
     * 算法名称 (如: SM4, AES等)
     */
    private String algorithm;

    /**
     * 参数数量
     */
    private Integer paramNum;

    /**
     * 算法参数字符串
     * 格式: "ECB,PKCS#5,0000000000000000" 或 "CBC,PKCS7,1234567890abcdef"
     */
    private String params;

    /**
     * 解析后的加密模式 (ECB, CBC等)
     */
    private String mode;

    /**
     * 解析后的填充方式 (PKCS5, PKCS7等)
     */
    private String padding;

    /**
     * 解析后的初始化向量或密钥
     */
    private String iv;

    /**
     * 解析参数字符串
     * 将params字符串解析为具体的参数
     */
    public void parseParams() {
        if (params == null || params.isEmpty()) {
            return;
        }

        String[] paramArray = params.split(",");
        if (paramArray.length >= 1) {
            this.mode = paramArray[0].trim();
        }
        if (paramArray.length >= 2) {
            this.padding = paramArray[1].trim();
        }
        if (paramArray.length >= 3) {
            this.iv = paramArray[2].trim();
        }
    }

    /**
     * 获取标准化的填充方式
     * 将PKCS#5转换为PKCS5等
     */
    public String getNormalizedPadding() {
        if (padding == null) {
            return null;
        }
        
        // 标准化填充方式名称
        String normalized = padding.replace("#", "").toUpperCase();
        
        // 处理常见的填充方式
        switch (normalized) {
            case "PKCS5":
            case "PKCS7":
                return "PKCS7"; // SM4使用PKCS7填充
            case "NOPADDING":
                return "NoPadding";
            default:
                return normalized;
        }
    }

    /**
     * 获取标准化的加密模式
     */
    public String getNormalizedMode() {
        if (mode == null) {
            return "ECB"; // 默认ECB模式
        }
        return mode.toUpperCase();
    }

    /**
     * 检查是否需要IV
     */
    public boolean requiresIV() {
        String normalizedMode = getNormalizedMode();
        return "CBC".equals(normalizedMode) || 
               "CFB".equals(normalizedMode) || 
               "OFB".equals(normalizedMode);
    }

    /**
     * 获取有效的IV
     * 如果模式需要IV但没有提供，则生成默认IV
     */
    public String getEffectiveIV() {
        if (!requiresIV()) {
            return null;
        }
        
        if (iv != null && !iv.isEmpty() && !"0000000000000000".equals(iv)) {
            // 确保IV长度正确（32个十六进制字符 = 16字节）
            if (iv.length() == 32) {
                return iv;
            } else if (iv.length() < 32) {
                // 不足32位用0填充
                return padWithZeros(iv, 32);
            } else {
                // 超过32位截取前32位
                return iv.substring(0, 32);
            }
        }
        
        // 如果没有提供有效IV，使用默认全零IV
        return "00000000000000000000000000000000";
    }

    /**
     * 验证算法配置是否有效
     */
    public boolean isValid() {
        return algorithm != null && !algorithm.isEmpty() &&
               paramNum != null && paramNum > 0 &&
               params != null && !params.isEmpty();
    }

    /**
     * 获取完整的算法转换字符串
     * 格式: Algorithm/Mode/Padding
     */
    public String getTransformation() {
        return String.format("%s/%s/%s", 
                           algorithm, 
                           getNormalizedMode(), 
                           getNormalizedPadding());
    }

    /**
     * 创建默认的SM4配置
     */
    public static AlgorithmConfigDTO createDefaultSM4() {
        AlgorithmConfigDTO config = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .paramNum(3)
                .params("CBC,PKCS7,00000000000000000000000000000000")
                .build();
        config.parseParams();
        return config;
    }

    /**
     * 从XML字符串创建配置对象
     */
    public static AlgorithmConfigDTO fromXml(String xmlContent) {
        if (xmlContent == null || xmlContent.isEmpty()) {
            return createDefaultSM4();
        }

        AlgorithmConfigDTO config = new AlgorithmConfigDTO();
        
        // 简单的XML解析（生产环境建议使用专业的XML解析库）
        config.setAlgorithm(extractXmlValue(xmlContent, "Algorithm"));
        
        String paramNumStr = extractXmlValue(xmlContent, "ParamNum");
        if (paramNumStr != null && !paramNumStr.isEmpty()) {
            try {
                config.setParamNum(Integer.parseInt(paramNumStr));
            } catch (NumberFormatException e) {
                config.setParamNum(3); // 默认值
            }
        }
        
        config.setParams(extractXmlValue(xmlContent, "Params"));
        
        // 解析参数
        config.parseParams();
        
        // 如果解析失败，返回默认配置
        if (!config.isValid()) {
            return createDefaultSM4();
        }
        
        return config;
    }

    /**
     * 简单的XML值提取方法
     */
    private static String extractXmlValue(String xml, String tagName) {
        String startTag = "<" + tagName + ">";
        String endTag = "</" + tagName + ">";
        
        int startIndex = xml.indexOf(startTag);
        if (startIndex == -1) {
            return null;
        }
        
        startIndex += startTag.length();
        int endIndex = xml.indexOf(endTag, startIndex);
        if (endIndex == -1) {
            return null;
        }
        
        return xml.substring(startIndex, endIndex).trim();
    }

    /**
     * 用零填充字符串到指定长度（JDK 8兼容）
     */
    private String padWithZeros(String str, int length) {
        if (str.length() >= length) {
            return str;
        }
        
        StringBuilder sb = new StringBuilder(str);
        while (sb.length() < length) {
            sb.append('0');
        }
        return sb.toString();
    }

    @Override
    public String toString() {
        return String.format("AlgorithmConfig{algorithm='%s', mode='%s', padding='%s', iv='%s'}", 
                           algorithm, getNormalizedMode(), getNormalizedPadding(), 
                           iv != null ? iv.substring(0, Math.min(8, iv.length())) + "..." : "null");
    }
}
