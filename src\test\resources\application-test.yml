# 测试环境配置
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  kafka:
    bootstrap-servers: ${spring.embedded.kafka.brokers}

# 测试环境日志
logging:
  level:
    com.suite.connector: DEBUG
    org.springframework.kafka: WARN
