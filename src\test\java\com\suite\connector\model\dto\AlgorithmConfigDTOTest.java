package com.suite.connector.model.dto;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 算法配置DTO测试类
 * 
 * <AUTHOR> Connector Team
 */
class AlgorithmConfigDTOTest {

    @Test
    void testParseParams() {
        // 测试参数解析
        AlgorithmConfigDTO config = new AlgorithmConfigDTO();
        config.setParams("ECB,PKCS#5,0000000000000000");
        config.parseParams();
        
        assertThat(config.getMode()).isEqualTo("ECB");
        assertThat(config.getPadding()).isEqualTo("PKCS#5");
        assertThat(config.getIv()).isEqualTo("0000000000000000");
    }

    @Test
    void testNormalizedPadding() {
        AlgorithmConfigDTO config = new AlgorithmConfigDTO();
        config.setPadding("PKCS#5");
        
        assertThat(config.getNormalizedPadding()).isEqualTo("PKCS7");
        
        config.setPadding("PKCS7");
        assertThat(config.getNormalizedPadding()).isEqualTo("PKCS7");
        
        config.setPadding("NoPadding");
        assertThat(config.getNormalizedPadding()).isEqualTo("NOPADDING");
    }

    @Test
    void testNormalizedMode() {
        AlgorithmConfigDTO config = new AlgorithmConfigDTO();
        config.setMode("cbc");
        
        assertThat(config.getNormalizedMode()).isEqualTo("CBC");
        
        config.setMode(null);
        assertThat(config.getNormalizedMode()).isEqualTo("ECB"); // 默认ECB
    }

    @Test
    void testRequiresIV() {
        AlgorithmConfigDTO config = new AlgorithmConfigDTO();
        
        config.setMode("CBC");
        assertThat(config.requiresIV()).isTrue();
        
        config.setMode("ECB");
        assertThat(config.requiresIV()).isFalse();
        
        config.setMode("CFB");
        assertThat(config.requiresIV()).isTrue();
    }

    @Test
    void testGetEffectiveIV() {
        AlgorithmConfigDTO config = new AlgorithmConfigDTO();
        config.setMode("CBC");
        
        // 测试有效IV
        config.setIv("1234567890abcdef1234567890abcdef");
        assertThat(config.getEffectiveIV()).isEqualTo("1234567890abcdef1234567890abcdef");
        
        // 测试短IV（自动填充）
        config.setIv("1234567890abcdef");
        String effectiveIV = config.getEffectiveIV();
        assertThat(effectiveIV).hasSize(32);
        assertThat(effectiveIV).startsWith("1234567890abcdef");
        
        // 测试长IV（自动截取）
        config.setIv("1234567890abcdef1234567890abcdef1234567890abcdef");
        assertThat(config.getEffectiveIV()).isEqualTo("1234567890abcdef1234567890abcdef");
        
        // 测试全零IV
        config.setIv("0000000000000000");
        assertThat(config.getEffectiveIV()).isEqualTo("00000000000000000000000000000000");
        
        // 测试null IV
        config.setIv(null);
        assertThat(config.getEffectiveIV()).isEqualTo("00000000000000000000000000000000");
    }

    @Test
    void testIsValid() {
        AlgorithmConfigDTO config = new AlgorithmConfigDTO();
        assertThat(config.isValid()).isFalse();
        
        config.setAlgorithm("SM4");
        assertThat(config.isValid()).isFalse();
        
        config.setParamNum(3);
        assertThat(config.isValid()).isFalse();
        
        config.setParams("ECB,PKCS7,0000000000000000");
        assertThat(config.isValid()).isTrue();
    }

    @Test
    void testGetTransformation() {
        AlgorithmConfigDTO config = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .params("CBC,PKCS#5,1234567890abcdef")
                .build();
        config.parseParams();
        
        assertThat(config.getTransformation()).isEqualTo("SM4/CBC/PKCS7");
    }

    @Test
    void testCreateDefaultSM4() {
        AlgorithmConfigDTO config = AlgorithmConfigDTO.createDefaultSM4();
        
        assertThat(config.getAlgorithm()).isEqualTo("SM4");
        assertThat(config.getParamNum()).isEqualTo(3);
        assertThat(config.getNormalizedMode()).isEqualTo("CBC");
        assertThat(config.getNormalizedPadding()).isEqualTo("PKCS7");
        assertThat(config.isValid()).isTrue();
    }

    @Test
    void testFromXml() {
        String xml = "<Root>\n" +
                    "    <Algorithm>SM4</Algorithm>\n" +
                    "    <ParamNum>3</ParamNum>\n" +
                    "    <Params>ECB,PKCS#5,0000000000000000</Params>\n" +
                    "</Root>";
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.fromXml(xml);
        
        assertThat(config.getAlgorithm()).isEqualTo("SM4");
        assertThat(config.getParamNum()).isEqualTo(3);
        assertThat(config.getParams()).isEqualTo("ECB,PKCS#5,0000000000000000");
        assertThat(config.getNormalizedMode()).isEqualTo("ECB");
        assertThat(config.getNormalizedPadding()).isEqualTo("PKCS7");
    }

    @Test
    void testFromXmlWithInvalidContent() {
        // 测试无效XML
        AlgorithmConfigDTO config1 = AlgorithmConfigDTO.fromXml("invalid xml");
        assertThat(config1.getAlgorithm()).isEqualTo("SM4"); // 应该返回默认配置
        
        // 测试空XML
        AlgorithmConfigDTO config2 = AlgorithmConfigDTO.fromXml("");
        assertThat(config2.getAlgorithm()).isEqualTo("SM4"); // 应该返回默认配置
        
        // 测试null XML
        AlgorithmConfigDTO config3 = AlgorithmConfigDTO.fromXml(null);
        assertThat(config3.getAlgorithm()).isEqualTo("SM4"); // 应该返回默认配置
    }

    @Test
    void testFromXmlWithMissingFields() {
        String xml = "<Root>\n" +
                    "    <Algorithm>SM4</Algorithm>\n" +
                    "    <!-- ParamNum missing -->\n" +
                    "    <Params>CBC,PKCS7,1234567890abcdef</Params>\n" +
                    "</Root>";
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.fromXml(xml);
        
        assertThat(config.getAlgorithm()).isEqualTo("SM4");
        assertThat(config.getParamNum()).isEqualTo(3); // 应该使用默认值
        assertThat(config.getParams()).isEqualTo("CBC,PKCS7,1234567890abcdef");
    }

    @Test
    void testRealWorldXmlExample() {
        // 测试真实的XML示例
        String xml = "<Root>\n" +
                    "\t<Algorithm>SM4</Algorithm>\n" +
                    "\t<ParamNum>3</ParamNum>\n" +
                    "\t<Params>ECB,PKCS#5,0000000000000000</Params>\n" +
                    "</Root>";
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.fromXml(xml);
        
        assertThat(config.isValid()).isTrue();
        assertThat(config.getAlgorithm()).isEqualTo("SM4");
        assertThat(config.getNormalizedMode()).isEqualTo("ECB");
        assertThat(config.getNormalizedPadding()).isEqualTo("PKCS7");
        assertThat(config.requiresIV()).isFalse(); // ECB模式不需要IV
    }

    @Test
    void testCBCModeExample() {
        // 测试CBC模式的XML示例
        String xml = "<Root>\n" +
                    "\t<Algorithm>SM4</Algorithm>\n" +
                    "\t<ParamNum>3</ParamNum>\n" +
                    "\t<Params>CBC,PKCS7,1234567890abcdef1234567890abcdef</Params>\n" +
                    "</Root>";
        
        AlgorithmConfigDTO config = AlgorithmConfigDTO.fromXml(xml);
        
        assertThat(config.isValid()).isTrue();
        assertThat(config.getAlgorithm()).isEqualTo("SM4");
        assertThat(config.getNormalizedMode()).isEqualTo("CBC");
        assertThat(config.getNormalizedPadding()).isEqualTo("PKCS7");
        assertThat(config.requiresIV()).isTrue(); // CBC模式需要IV
        assertThat(config.getEffectiveIV()).isEqualTo("1234567890abcdef1234567890abcdef");
    }

    @Test
    void testToString() {
        AlgorithmConfigDTO config = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .params("CBC,PKCS7,1234567890abcdef1234567890abcdef")
                .build();
        config.parseParams();
        
        String str = config.toString();
        assertThat(str).contains("SM4");
        assertThat(str).contains("CBC");
        assertThat(str).contains("PKCS7");
        assertThat(str).contains("12345678..."); // IV应该被截断显示
    }
}
