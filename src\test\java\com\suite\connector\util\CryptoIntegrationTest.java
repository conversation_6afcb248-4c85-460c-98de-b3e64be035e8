package com.suite.connector.util;

import com.suite.connector.model.dto.AlgorithmConfigDTO;
import com.suite.connector.service.CryptoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 加密功能集成测试
 * 验证动态算法配置和JDK 8兼容性
 * 
 * <AUTHOR> Connector Team
 */
@SpringBootTest
@ActiveProfiles("test")
class CryptoIntegrationTest {

    private CryptoService cryptoService;

    @BeforeEach
    void setUp() {
        cryptoService = new CryptoService();
    }

    @Test
    void testCompleteWorkflow() {
        // 测试完整的工作流程：XML解析 -> 算法配置 -> 加密计算
        
        // 1. 模拟套件返回的算法配置XML
        String algorithmXml = "<Root>\n" +
                             "\t<Algorithm>SM4</Algorithm>\n" +
                             "\t<ParamNum>3</ParamNum>\n" +
                             "\t<Params>ECB,PKCS#5,0000000000000000</Params>\n" +
                             "</Root>";
        
        // 2. 解析算法配置
        AlgorithmConfigDTO algorithmConfig = cryptoService.parseAlgorithmConfig(algorithmXml);
        assertThat(algorithmConfig).isNotNull();
        assertThat(algorithmConfig.getAlgorithm()).isEqualTo("SM4");
        assertThat(algorithmConfig.getNormalizedMode()).isEqualTo("ECB");
        assertThat(algorithmConfig.getNormalizedPadding()).isEqualTo("PKCS7");
        
        // 3. 使用动态算法配置计算认证key
        String suiteSecret = "test_suite_secret_123";
        String requestUuid = cryptoService.generateUUID();
        
        String authKey = cryptoService.calculateKeyWithAlgorithm(suiteSecret, requestUuid, algorithmConfig);
        assertThat(authKey).isNotNull();
        assertThat(authKey).isNotEmpty();
        
        // 4. 验证一致性
        String authKey2 = cryptoService.calculateKeyWithAlgorithm(suiteSecret, requestUuid, algorithmConfig);
        assertThat(authKey).isEqualTo(authKey2);
        
        // 5. 测试数据加密解密
        String sensitiveData = "This is sensitive data that needs encryption";
        String encryptedData = cryptoService.encryptWithAlgorithm(sensitiveData, suiteSecret, algorithmConfig);
        String decryptedData = cryptoService.decryptWithAlgorithm(encryptedData, suiteSecret, algorithmConfig);
        
        assertThat(encryptedData).isNotEqualTo(sensitiveData);
        assertThat(decryptedData).isEqualTo(sensitiveData);
    }

    @Test
    void testCBCModeWorkflow() {
        // 测试CBC模式的完整工作流程
        
        String algorithmXml = "<Root>\n" +
                             "\t<Algorithm>SM4</Algorithm>\n" +
                             "\t<ParamNum>3</ParamNum>\n" +
                             "\t<Params>CBC,PKCS7,1234567890abcdef1234567890abcdef</Params>\n" +
                             "</Root>";
        
        AlgorithmConfigDTO algorithmConfig = cryptoService.parseAlgorithmConfig(algorithmXml);
        assertThat(algorithmConfig.getNormalizedMode()).isEqualTo("CBC");
        assertThat(algorithmConfig.requiresIV()).isTrue();
        assertThat(algorithmConfig.getEffectiveIV()).isEqualTo("1234567890abcdef1234567890abcdef");
        
        String suiteSecret = "cbc_test_secret";
        String requestUuid = cryptoService.generateUUID();
        
        String authKey = cryptoService.calculateKeyWithAlgorithm(suiteSecret, requestUuid, algorithmConfig);
        assertThat(authKey).isNotNull();
        
        // 测试数据加密解密
        String testData = "CBC mode test data";
        String encrypted = cryptoService.encryptWithAlgorithm(testData, suiteSecret, algorithmConfig);
        String decrypted = cryptoService.decryptWithAlgorithm(encrypted, suiteSecret, algorithmConfig);
        
        assertThat(decrypted).isEqualTo(testData);
    }

    @Test
    void testInvalidXmlFallback() {
        // 测试无效XML时的降级处理
        
        String invalidXml = "This is not valid XML";
        AlgorithmConfigDTO config = cryptoService.parseAlgorithmConfig(invalidXml);
        
        // 应该返回默认的SM4配置
        assertThat(config).isNotNull();
        assertThat(config.getAlgorithm()).isEqualTo("SM4");
        assertThat(config.isValid()).isTrue();
        
        // 使用默认配置应该能正常工作
        String secret = "fallback_test";
        String uuid = cryptoService.generateUUID();
        String authKey = cryptoService.calculateKeyWithAlgorithm(secret, uuid, config);
        
        assertThat(authKey).isNotNull();
    }

    @Test
    void testDynamicCryptoUtilDirectly() {
        // 直接测试DynamicCryptoUtil的功能
        
        String plaintext = "Direct test of DynamicCryptoUtil";
        String key = SM4Util.generateKey();
        
        // ECB模式测试
        AlgorithmConfigDTO ecbConfig = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .paramNum(3)
                .params("ECB,PKCS7,0000000000000000")
                .build();
        ecbConfig.parseParams();
        
        String ciphertext = DynamicCryptoUtil.encrypt(plaintext, key, ecbConfig);
        String decrypted = DynamicCryptoUtil.decrypt(ciphertext, key, ecbConfig);
        
        assertThat(decrypted).isEqualTo(plaintext);
        
        // CBC模式测试
        AlgorithmConfigDTO cbcConfig = AlgorithmConfigDTO.builder()
                .algorithm("SM4")
                .paramNum(3)
                .params("CBC,PKCS7,fedcba0987654321fedcba0987654321")
                .build();
        cbcConfig.parseParams();
        
        String cbcCiphertext = DynamicCryptoUtil.encrypt(plaintext, key, cbcConfig);
        String cbcDecrypted = DynamicCryptoUtil.decrypt(cbcCiphertext, key, cbcConfig);
        
        assertThat(cbcDecrypted).isEqualTo(plaintext);
        
        // ECB和CBC的结果应该不同
        assertThat(ciphertext).isNotEqualTo(cbcCiphertext);
    }

    @Test
    void testJDK8Compatibility() {
        // 测试JDK 8兼容性（主要是字符串处理）
        
        AlgorithmConfigDTO config = new AlgorithmConfigDTO();
        config.setIv("1234"); // 短IV，需要填充
        config.setMode("CBC");
        config.parseParams();
        
        String effectiveIV = config.getEffectiveIV();
        assertThat(effectiveIV).hasSize(32); // 应该被填充到32位
        assertThat(effectiveIV).startsWith("1234");
        
        // 验证填充字符是'0'
        for (int i = 4; i < 32; i++) {
            assertThat(effectiveIV.charAt(i)).isEqualTo('0');
        }
    }

    @Test
    void testRealWorldScenario() {
        // 模拟真实的套件对接场景
        
        // 1. 套件返回的算法配置（真实格式）
        String realWorldXml = "<Root>" +
                             "<Algorithm>SM4</Algorithm>" +
                             "<ParamNum>3</ParamNum>" +
                             "<Params>ECB,PKCS#5,0000000000000000</Params>" +
                             "</Root>";
        
        // 2. 解析配置
        AlgorithmConfigDTO config = AlgorithmConfigDTO.fromXml(realWorldXml);
        assertThat(config.isValid()).isTrue();
        
        // 3. 验证配置
        assertThat(DynamicCryptoUtil.validateAlgorithmConfig(config)).isTrue();
        
        // 4. 计算认证key（模拟真实的套件秘钥和UUID）
        String suiteSecret = "real_suite_secret_key_12345";
        String requestUuid = "550e8400-e29b-41d4-a716-************";
        
        String authKey = cryptoService.calculateKeyWithAlgorithm(suiteSecret, requestUuid, config);
        
        // 5. 验证key的有效性
        assertThat(authKey).isNotNull();
        assertThat(authKey).isNotEmpty();
        assertThat(authKey.length()).isGreaterThan(0);
        
        // 6. 验证一致性（相同输入应该产生相同输出）
        String authKey2 = cryptoService.calculateKeyWithAlgorithm(suiteSecret, requestUuid, config);
        assertThat(authKey).isEqualTo(authKey2);
        
        // 7. 验证不同输入产生不同输出
        String differentUuid = "550e8400-e29b-41d4-a716-************";
        String differentKey = cryptoService.calculateKeyWithAlgorithm(suiteSecret, differentUuid, config);
        assertThat(authKey).isNotEqualTo(differentKey);
    }
}
