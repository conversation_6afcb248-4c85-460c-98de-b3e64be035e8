package com.suite.connector.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * 套件配置数据传输对象
 * 用于解析datasource_access表中的params字段
 * 
 * <AUTHOR> Connector Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SuiteConfigDTO {

    /**
     * 套件ID
     */
    @NotBlank(message = "套件ID不能为空")
    private String suiteId;

    /**
     * 套件访问密钥
     */
    @NotBlank(message = "密钥不能为空")
    @JsonProperty("secret")
    private String secret;

    /**
     * 套件API基础URL
     * 格式: https://127.0.0.1:5443/api
     */
    @NotBlank(message = "URL不能为空")
    @JsonProperty("url")
    private String url;

    /**
     * 协议类型 (http/https)
     */
    private String protocol;

    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * API路径前缀
     */
    private String pathPrefix;

    /**
     * 数据类型列表
     */
    private String[] dataTypes;

    /**
     * 子类型列表
     */
    private String[] subTypes;

    /**
     * 分页大小
     */
    @Builder.Default
    private Integer pageSize = 1000;

    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 从URL解析协议、主机、端口等信息
     */
    public void parseUrl() {
        if (this.url == null || this.url.isEmpty()) {
            return;
        }

        try {
            URL parsedUrl = new URL(this.url);
            this.protocol = parsedUrl.getProtocol();
            this.host = parsedUrl.getHost();
            this.port = parsedUrl.getPort();
            
            // 如果端口为-1，使用默认端口
            if (this.port == -1) {
                this.port = parsedUrl.getDefaultPort();
            }
            
            this.pathPrefix = parsedUrl.getPath();
            if (this.pathPrefix == null || this.pathPrefix.isEmpty()) {
                this.pathPrefix = "/";
            }
            
        } catch (MalformedURLException e) {
            throw new IllegalArgumentException("无效的URL格式: " + this.url, e);
        }
    }

    /**
     * 获取基础URL（不包含路径）
     */
    public String getBaseUrl() {
        if (protocol != null && host != null && port != null) {
            return String.format("%s://%s:%d", protocol, host, port);
        }
        return url;
    }

    /**
     * 构建完整的API URL
     */
    public String buildApiUrl(String apiPath) {
        String baseUrl = getBaseUrl();
        String prefix = pathPrefix != null ? pathPrefix : "";
        
        // 确保路径正确拼接
        if (!prefix.endsWith("/") && !apiPath.startsWith("/")) {
            prefix += "/";
        } else if (prefix.endsWith("/") && apiPath.startsWith("/")) {
            apiPath = apiPath.substring(1);
        }
        
        return baseUrl + prefix + apiPath;
    }

    /**
     * 生成Kafka主题名称
     */
    public String generateTopicName() {
        if (host != null && port != null) {
            return String.format("suite_%s_%d", host.replace(".", "_"), port);
        }
        return "suite_" + suiteId;
    }

    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return suiteId != null && !suiteId.isEmpty() &&
               secret != null && !secret.isEmpty() &&
               url != null && !url.isEmpty();
    }

    /**
     * 创建默认配置
     */
    public static SuiteConfigDTO createDefault(String suiteId) {
        return SuiteConfigDTO.builder()
                .suiteId(suiteId)
                .secret("")
                .url("")
                .dataTypes(new String[]{"default"})
                .subTypes(new String[]{"default"})
                .pageSize(1000)
                .enabled(true)
                .build();
    }

    /**
     * 从JSON字符串解析配置
     */
    public static SuiteConfigDTO fromJson(String suiteId, String jsonParams) {
        // 简单的JSON解析实现
        // 实际项目中建议使用Jackson ObjectMapper
        SuiteConfigDTO config = new SuiteConfigDTO();
        config.setSuiteId(suiteId);
        
        if (jsonParams != null && !jsonParams.isEmpty()) {
            // 解析secret
            String secret = extractJsonValue(jsonParams, "secret");
            if (secret != null) {
                config.setSecret(secret);
            }
            
            // 解析url
            String url = extractJsonValue(jsonParams, "url");
            if (url != null) {
                config.setUrl(url);
                config.parseUrl();
            }
        }
        
        return config;
    }

    /**
     * 简单的JSON值提取方法
     */
    private static String extractJsonValue(String json, String key) {
        String pattern = "\"" + key + "\":\"";
        int start = json.indexOf(pattern);
        if (start != -1) {
            start += pattern.length();
            int end = json.indexOf("\"", start);
            if (end != -1) {
                return json.substring(start, end);
            }
        }
        return null;
    }

    /**
     * 转换为JSON字符串
     */
    public String toJson() {
        return String.format("{\"secret\":\"%s\",\"url\":\"%s\"}", 
                           secret != null ? secret : "", 
                           url != null ? url : "");
    }
}
