package com.suite.connector.config;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.security.Security;

/**
 * 国密算法配置类
 * 配置BouncyCastle加密提供者
 * 
 * <AUTHOR> Connector Team
 */
@Slf4j
@Configuration
public class CryptoConfig {

    /**
     * 初始化BouncyCastle加密提供者
     */
    @PostConstruct
    public void initCryptoProvider() {
        // 添加BouncyCastle作为安全提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
            log.info("BouncyCastle加密提供者已添加");
        } else {
            log.info("BouncyCastle加密提供者已存在");
        }
        
        // 打印所有可用的加密提供者
        if (log.isDebugEnabled()) {
            log.debug("可用的安全提供者:");
            for (int i = 0; i < Security.getProviders().length; i++) {
                log.debug("  {}: {}", i + 1, Security.getProviders()[i].getName());
            }
        }
    }

    /**
     * 创建加密提供者Bean
     * 
     * @return BouncyCastle提供者实例
     */
    @Bean
    public BouncyCastleProvider bouncyCastleProvider() {
        return new BouncyCastleProvider();
    }
}
