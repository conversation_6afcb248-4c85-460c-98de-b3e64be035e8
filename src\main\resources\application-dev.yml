# 开发环境配置
spring:
  datasource:
    url: ********************************************************************************************************************************
    username: root
    password: 123456
  
  jpa:
    hibernate:
      ddl-auto: create-drop  # 开发环境自动创建表结构
    show-sql: true          # 显示SQL语句
  
  kafka:
    bootstrap-servers: localhost:9092

# 开发环境日志级别
logging:
  level:
    com.suite.connector: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 开发环境套件配置
suite:
  connector:
    scheduler:
      sync-interval: 60000   # 开发环境1分钟同步一次
      initial-delay: 10000   # 启动后10秒开始
    http:
      connect-timeout: 5s
      read-timeout: 15s
