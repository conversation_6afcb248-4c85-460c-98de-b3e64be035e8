-- 套件数据同步服务数据库初始化脚本
-- 版本: V1.0.0
-- 作者: Suite Connector Team

-- 创建套件同步进度记录表
CREATE TABLE IF NOT EXISTS suite_offset (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    suite_id VARCHAR(64) NOT NULL COMMENT '套件ID',
    dept_id VARCHAR(64) NOT NULL COMMENT '部门ID',
    host_code VARCHAR(64) NOT NULL COMMENT '终端主机代码',
    data_type VARCHAR(32) NOT NULL COMMENT '数据类型',
    sub_type VARCHAR(32) NOT NULL COMMENT '数据子类型',
    offset_value BIGINT DEFAULT 0 COMMENT '偏移量值，记录同步进度',
    first_time DATETIME COMMENT '首次同步时间',
    last_time DATETIME COMMENT '最后同步时间',
    op_status VARCHAR(16) DEFAULT 'SUCCESS' COMMENT '操作状态：SUCCESS, FAILED, PROCESSING',
    err_msg TEXT COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 创建唯一索引，确保同一套件、部门、终端、数据类型、子类型的记录唯一
    UNIQUE KEY uk_suite_offset (suite_id, dept_id, host_code, data_type, sub_type),
    
    -- 创建普通索引提高查询性能
    INDEX idx_suite_id (suite_id),
    INDEX idx_dept_id (dept_id),
    INDEX idx_host_code (host_code),
    INDEX idx_op_status (op_status),
    INDEX idx_last_time (last_time),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套件数据同步进度记录表';

-- 创建数据源访问记录表
CREATE TABLE IF NOT EXISTS datasource_access (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    suite_id VARCHAR(64) NOT NULL UNIQUE COMMENT '套件ID，唯一标识',
    topic VARCHAR(128) NOT NULL COMMENT 'Kafka主题名称',
    total BIGINT DEFAULT 0 COMMENT '总数据量',
    status VARCHAR(16) DEFAULT 'SUCCESS' COMMENT '同步状态：SUCCESS, FAILED, PROCESSING',
    first_time DATETIME COMMENT '首次同步时间',
    last_time DATETIME COMMENT '最后同步时间',
    params TEXT COMMENT '套件配置参数，JSON格式',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 创建唯一索引
    UNIQUE KEY uk_suite_id (suite_id),
    
    -- 创建普通索引
    INDEX idx_topic (topic),
    INDEX idx_status (status),
    INDEX idx_last_time (last_time),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据源访问记录表';

-- 插入示例数据（可选，用于开发测试）
-- INSERT INTO datasource_access (suite_id, topic, params) VALUES 
-- ('suite_001', 'suite_127_0_0_1_5443', '{"secret":"test_secret","url":"https://127.0.0.1:5443/api"}'),
-- ('suite_002', 'suite_192_168_1_100_8080', '{"secret":"test_secret_2","url":"http://192.168.1.100:8080/api"}');

-- 创建视图：套件同步状态概览
CREATE OR REPLACE VIEW v_suite_sync_overview AS
SELECT 
    dsa.suite_id,
    dsa.topic,
    dsa.status as suite_status,
    dsa.total as total_records,
    dsa.first_time as suite_first_time,
    dsa.last_time as suite_last_time,
    COUNT(so.id) as offset_records_count,
    SUM(CASE WHEN so.op_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN so.op_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
    SUM(CASE WHEN so.op_status = 'PROCESSING' THEN 1 ELSE 0 END) as processing_count,
    SUM(so.offset_value) as total_offset_value,
    MAX(so.last_time) as latest_sync_time
FROM datasource_access dsa
LEFT JOIN suite_offset so ON dsa.suite_id = so.suite_id
GROUP BY dsa.suite_id, dsa.topic, dsa.status, dsa.total, dsa.first_time, dsa.last_time;

-- 添加表注释
ALTER TABLE suite_offset COMMENT = '套件数据同步进度记录表 - 记录每个套件、部门、终端、数据类型的同步进度';
ALTER TABLE datasource_access COMMENT = '数据源访问记录表 - 记录套件的整体同步状态和配置信息';
