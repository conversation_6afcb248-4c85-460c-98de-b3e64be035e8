package com.suite.connector.util;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * SM4工具类测试
 * 
 * <AUTHOR> Connector Team
 */
class SM4UtilTest {

    @Test
    void testGenerateKey() {
        // 测试密钥生成
        String key1 = SM4Util.generateKey();
        String key2 = SM4Util.generateKey();
        
        assertThat(key1).isNotNull();
        assertThat(key2).isNotNull();
        assertThat(key1).hasSize(32); // 16字节 = 32个十六进制字符
        assertThat(key1).isNotEqualTo(key2); // 每次生成的密钥应该不同
        assertThat(key1).matches("^[0-9a-f]+$"); // 只包含小写十六进制字符
    }

    @Test
    void testGenerateIV() {
        // 测试初始化向量生成
        String iv1 = SM4Util.generateIV();
        String iv2 = SM4Util.generateIV();
        
        assertThat(iv1).isNotNull();
        assertThat(iv2).isNotNull();
        assertThat(iv1).hasSize(32); // 16字节 = 32个十六进制字符
        assertThat(iv1).isNotEqualTo(iv2); // 每次生成的IV应该不同
        assertThat(iv1).matches("^[0-9a-f]+$"); // 只包含小写十六进制字符
    }

    @Test
    void testEncryptDecrypt() {
        // 测试加密解密
        String plaintext = "Hello, SM4 Encryption!";
        String key = SM4Util.generateKey();
        String iv = SM4Util.generateIV();
        
        // 加密
        String ciphertext = SM4Util.encrypt(plaintext, key, iv);
        assertThat(ciphertext).isNotNull();
        assertThat(ciphertext).isNotEqualTo(plaintext);
        
        // 解密
        String decrypted = SM4Util.decrypt(ciphertext, key, iv);
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptDecryptWithFixedIV() {
        // 测试使用固定IV的加密解密
        String plaintext = "Test with fixed IV";
        String key = SM4Util.generateKey();
        
        // 加密
        String ciphertext = SM4Util.encryptWithFixedIV(plaintext, key);
        assertThat(ciphertext).isNotNull();
        assertThat(ciphertext).isNotEqualTo(plaintext);
        
        // 解密
        String decrypted = SM4Util.decryptWithFixedIV(ciphertext, key);
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptConsistency() {
        // 测试相同输入的加密一致性（使用相同的key和iv）
        String plaintext = "Consistency test";
        String key = "1234567890abcdef1234567890abcdef"; // 固定密钥
        String iv = "fedcba0987654321fedcba0987654321";   // 固定IV
        
        String ciphertext1 = SM4Util.encrypt(plaintext, key, iv);
        String ciphertext2 = SM4Util.encrypt(plaintext, key, iv);
        
        assertThat(ciphertext1).isEqualTo(ciphertext2);
    }

    @Test
    void testEncryptDifferentKeys() {
        // 测试不同密钥产生不同密文
        String plaintext = "Same plaintext";
        String key1 = SM4Util.generateKey();
        String key2 = SM4Util.generateKey();
        String iv = SM4Util.generateIV();
        
        String ciphertext1 = SM4Util.encrypt(plaintext, key1, iv);
        String ciphertext2 = SM4Util.encrypt(plaintext, key2, iv);
        
        assertThat(ciphertext1).isNotEqualTo(ciphertext2);
    }

    @Test
    void testGenerateKeyFromPassword() {
        // 测试从密码生成密钥
        String password = "my_secure_password";
        String key1 = SM4Util.generateKeyFromPassword(password);
        String key2 = SM4Util.generateKeyFromPassword(password);
        
        assertThat(key1).isNotNull();
        assertThat(key1).hasSize(32);
        assertThat(key1).isEqualTo(key2); // 相同密码应该生成相同密钥
        
        // 不同密码生成不同密钥
        String key3 = SM4Util.generateKeyFromPassword("different_password");
        assertThat(key1).isNotEqualTo(key3);
    }

    @Test
    void testIsValidKey() {
        // 测试密钥验证
        String validKey = SM4Util.generateKey();
        String invalidKey1 = "invalid_key";
        String invalidKey2 = "1234567890abcdef"; // 长度不够
        String invalidKey3 = "1234567890abcdef1234567890abcdefg"; // 包含非十六进制字符
        String invalidKey4 = null;
        
        assertThat(SM4Util.isValidKey(validKey)).isTrue();
        assertThat(SM4Util.isValidKey(invalidKey1)).isFalse();
        assertThat(SM4Util.isValidKey(invalidKey2)).isFalse();
        assertThat(SM4Util.isValidKey(invalidKey3)).isFalse();
        assertThat(SM4Util.isValidKey(invalidKey4)).isFalse();
    }

    @Test
    void testIsValidIV() {
        // 测试IV验证
        String validIV = SM4Util.generateIV();
        String invalidIV1 = "invalid_iv";
        String invalidIV2 = "1234567890abcdef"; // 长度不够
        String invalidIV3 = null;
        
        assertThat(SM4Util.isValidIV(validIV)).isTrue();
        assertThat(SM4Util.isValidIV(invalidIV1)).isFalse();
        assertThat(SM4Util.isValidIV(invalidIV2)).isFalse();
        assertThat(SM4Util.isValidIV(invalidIV3)).isFalse();
    }

    @Test
    void testEncryptNullInputs() {
        // 测试null输入
        String key = SM4Util.generateKey();
        String iv = SM4Util.generateIV();
        
        assertThatThrownBy(() -> SM4Util.encrypt(null, key, iv))
                .isInstanceOf(IllegalArgumentException.class);
        
        assertThatThrownBy(() -> SM4Util.encrypt("test", null, iv))
                .isInstanceOf(IllegalArgumentException.class);
        
        assertThatThrownBy(() -> SM4Util.encrypt("test", key, null))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    void testDecryptNullInputs() {
        // 测试解密null输入
        String key = SM4Util.generateKey();
        String iv = SM4Util.generateIV();
        
        assertThatThrownBy(() -> SM4Util.decrypt(null, key, iv))
                .isInstanceOf(IllegalArgumentException.class);
        
        assertThatThrownBy(() -> SM4Util.decrypt("test", null, iv))
                .isInstanceOf(IllegalArgumentException.class);
        
        assertThatThrownBy(() -> SM4Util.decrypt("test", key, null))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    void testEncryptEmptyString() {
        // 测试加密空字符串
        String plaintext = "";
        String key = SM4Util.generateKey();
        String iv = SM4Util.generateIV();
        
        String ciphertext = SM4Util.encrypt(plaintext, key, iv);
        String decrypted = SM4Util.decrypt(ciphertext, key, iv);
        
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptLongText() {
        // 测试加密长文本
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("This is a long text for testing SM4 encryption. ");
        }
        String plaintext = sb.toString();
        
        String key = SM4Util.generateKey();
        String iv = SM4Util.generateIV();
        
        String ciphertext = SM4Util.encrypt(plaintext, key, iv);
        String decrypted = SM4Util.decrypt(ciphertext, key, iv);
        
        assertThat(decrypted).isEqualTo(plaintext);
    }

    @Test
    void testEncryptSpecialCharacters() {
        // 测试加密特殊字符
        String plaintext = "特殊字符测试: !@#$%^&*()_+-=[]{}|;':\",./<>?`~";
        String key = SM4Util.generateKey();
        String iv = SM4Util.generateIV();
        
        String ciphertext = SM4Util.encrypt(plaintext, key, iv);
        String decrypted = SM4Util.decrypt(ciphertext, key, iv);
        
        assertThat(decrypted).isEqualTo(plaintext);
    }
}
