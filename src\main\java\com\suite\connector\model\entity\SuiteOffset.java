package com.suite.connector.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 套件数据同步进度记录实体
 * 记录每个套件、部门、终端、数据类型的同步进度
 * 
 * <AUTHOR> Connector Team
 */
@Entity
@Table(name = "suite_offset", 
       uniqueConstraints = @UniqueConstraint(
           name = "uk_suite_offset", 
           columnNames = {"suite_id", "dept_id", "host_code", "data_type", "sub_type"}
       ))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SuiteOffset {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 套件ID
     */
    @Column(name = "suite_id", nullable = false, length = 64)
    private String suiteId;

    /**
     * 部门ID
     */
    @Column(name = "dept_id", nullable = false, length = 64)
    private String deptId;

    /**
     * 终端主机代码
     */
    @Column(name = "host_code", nullable = false, length = 64)
    private String hostCode;

    /**
     * 数据类型
     */
    @Column(name = "data_type", nullable = false, length = 32)
    private String dataType;

    /**
     * 数据子类型
     */
    @Column(name = "sub_type", nullable = false, length = 32)
    private String subType;

    /**
     * 偏移量值，记录同步进度
     */
    @Column(name = "offset_value")
    @Builder.Default
    private Long offsetValue = 0L;

    /**
     * 首次同步时间
     */
    @Column(name = "first_time")
    private LocalDateTime firstTime;

    /**
     * 最后同步时间
     */
    @Column(name = "last_time")
    private LocalDateTime lastTime;

    /**
     * 操作状态：SUCCESS, FAILED, PROCESSING
     */
    @Column(name = "op_status", length = 16)
    @Builder.Default
    private String opStatus = "SUCCESS";

    /**
     * 错误信息
     */
    @Column(name = "err_msg", columnDefinition = "TEXT")
    private String errMsg;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;

    /**
     * 操作状态枚举
     */
    public enum OpStatus {
        SUCCESS("SUCCESS"),
        FAILED("FAILED"),
        PROCESSING("PROCESSING");

        private final String value;

        OpStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 更新同步进度
     */
    public void updateProgress(Long newOffset, LocalDateTime syncTime, String status, String errorMsg) {
        this.offsetValue = newOffset;
        this.lastTime = syncTime;
        this.opStatus = status;
        this.errMsg = errorMsg;
        
        // 如果是首次同步，设置首次时间
        if (this.firstTime == null) {
            this.firstTime = syncTime;
        }
    }

    /**
     * 标记为处理中状态
     */
    public void markAsProcessing() {
        this.opStatus = OpStatus.PROCESSING.getValue();
        this.errMsg = null;
    }

    /**
     * 标记为成功状态
     */
    public void markAsSuccess(Long newOffset, LocalDateTime syncTime) {
        updateProgress(newOffset, syncTime, OpStatus.SUCCESS.getValue(), null);
    }

    /**
     * 标记为失败状态
     */
    public void markAsFailed(String errorMsg) {
        this.opStatus = OpStatus.FAILED.getValue();
        this.errMsg = errorMsg;
        this.lastTime = LocalDateTime.now();
    }
}
