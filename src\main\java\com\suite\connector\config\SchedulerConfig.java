package com.suite.connector.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 定时任务调度器配置
 * 
 * <AUTHOR> Connector Team
 */
@Slf4j
@Configuration
public class SchedulerConfig {

    @Value("${suite.connector.scheduler.thread-pool-size:10}")
    private int threadPoolSize;

    @Bean(name = "suiteTaskScheduler")
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(threadPoolSize);
        scheduler.setThreadNamePrefix("suite-sync-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.setRejectedExecutionHandler((r, executor) -> {
            log.warn("Task rejected, thread pool is full and queue is also full");
        });
        scheduler.initialize();
        
        log.info("Suite task scheduler initialized with pool size: {}", threadPoolSize);
        return scheduler;
    }
}
