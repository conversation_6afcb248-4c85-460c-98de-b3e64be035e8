package com.suite.connector.util;

import com.suite.connector.model.dto.AlgorithmConfigDTO;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * 动态加密工具类
 * 根据算法配置动态选择加密方式
 * 
 * <AUTHOR> Connector Team
 */
@Slf4j
public class DynamicCryptoUtil {

    /**
     * 根据算法配置进行加密
     *
     * @param plaintext 明文
     * @param key 密钥（十六进制字符串）
     * @param algorithmConfig 算法配置
     * @return 加密后的密文（Base64编码）
     */
    public static String encrypt(String plaintext, String key, AlgorithmConfigDTO algorithmConfig) {
        if (plaintext == null || key == null || algorithmConfig == null) {
            throw new IllegalArgumentException("参数不能为null");
        }

        if (!algorithmConfig.isValid()) {
            throw new IllegalArgumentException("无效的算法配置");
        }

        try {
            // 根据算法配置选择加密方式
            String algorithm = algorithmConfig.getAlgorithm().toUpperCase();
            String mode = algorithmConfig.getNormalizedMode();

            if ("SM4".equals(algorithm)) {
                return encryptWithSM4(plaintext, key, algorithmConfig);
            } else {
                throw new UnsupportedOperationException("不支持的算法: " + algorithm);
            }

        } catch (Exception e) {
            log.error("动态加密失败: algorithm={}, mode={}, padding={}",
                     algorithmConfig.getAlgorithm(),
                     algorithmConfig.getNormalizedMode(),
                     algorithmConfig.getNormalizedPadding(), e);
            throw new RuntimeException("动态加密失败", e);
        }
    }

    /**
     * 根据算法配置进行解密
     *
     * @param ciphertext 密文（Base64编码）
     * @param key 密钥（十六进制字符串）
     * @param algorithmConfig 算法配置
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext, String key, AlgorithmConfigDTO algorithmConfig) {
        if (ciphertext == null || key == null || algorithmConfig == null) {
            throw new IllegalArgumentException("参数不能为null");
        }

        if (!algorithmConfig.isValid()) {
            throw new IllegalArgumentException("无效的算法配置");
        }

        try {
            // 根据算法配置选择解密方式
            String algorithm = algorithmConfig.getAlgorithm().toUpperCase();

            if ("SM4".equals(algorithm)) {
                return decryptWithSM4(ciphertext, key, algorithmConfig);
            } else {
                throw new UnsupportedOperationException("不支持的算法: " + algorithm);
            }

        } catch (Exception e) {
            log.error("动态解密失败: algorithm={}, mode={}, padding={}",
                     algorithmConfig.getAlgorithm(),
                     algorithmConfig.getNormalizedMode(),
                     algorithmConfig.getNormalizedPadding(), e);
            throw new RuntimeException("动态解密失败", e);
        }
    }

    /**
     * 使用SM4算法加密
     */
    private static String encryptWithSM4(String plaintext, String key, AlgorithmConfigDTO config) {
        String mode = config.getNormalizedMode();

        if ("ECB".equals(mode)) {
            // ECB模式：使用固定IV的简化方法
            return SM4Util.encryptWithFixedIV(plaintext, key);
        } else if ("CBC".equals(mode)) {
            // CBC模式：使用指定的IV
            String iv = config.getEffectiveIV();
            return SM4Util.encrypt(plaintext, key, iv);
        } else {
            throw new UnsupportedOperationException("不支持的SM4模式: " + mode);
        }
    }

    /**
     * 使用SM4算法解密
     */
    private static String decryptWithSM4(String ciphertext, String key, AlgorithmConfigDTO config) {
        String mode = config.getNormalizedMode();

        if ("ECB".equals(mode)) {
            // ECB模式：使用固定IV的简化方法
            return SM4Util.decryptWithFixedIV(ciphertext, key);
        } else if ("CBC".equals(mode)) {
            // CBC模式：使用指定的IV
            String iv = config.getEffectiveIV();
            return SM4Util.decrypt(ciphertext, key, iv);
        } else {
            throw new UnsupportedOperationException("不支持的SM4模式: " + mode);
        }
    }

    /**
     * 验证算法是否支持
     */
    public static boolean isSupportedAlgorithm(String algorithm) {
        if (algorithm == null) {
            return false;
        }

        switch (algorithm.toUpperCase()) {
            case "SM4":
                return true;
            default:
                return false;
        }
    }

    /**
     * 验证加密模式是否支持
     */
    public static boolean isSupportedMode(String mode) {
        if (mode == null) {
            return false;
        }

        switch (mode.toUpperCase()) {
            case "ECB":
            case "CBC":
                return true;
            default:
                return false;
        }
    }

    /**
     * 验证填充方式是否支持
     */
    public static boolean isSupportedPadding(String padding) {
        if (padding == null) {
            return false;
        }

        switch (padding.toUpperCase()) {
            case "PKCS7":
            case "PKCS5":
                return true;
            default:
                return false;
        }
    }

    /**
     * 验证密钥长度是否符合算法要求
     */
    public static boolean isValidKeyLength(String algorithm, byte[] key) {
        if (algorithm == null || key == null) {
            return false;
        }

        switch (algorithm.toUpperCase()) {
            case "SM4":
                return key.length == 16; // SM4需要16字节密钥
            default:
                return false;
        }
    }

    /**
     * 验证IV长度是否符合算法要求
     */
    public static boolean isValidIVLength(String algorithm, byte[] iv) {
        if (algorithm == null || iv == null) {
            return false;
        }

        switch (algorithm.toUpperCase()) {
            case "SM4":
                return iv.length == 16; // SM4需要16字节IV
            default:
                return false;
        }
    }

    /**
     * 获取算法的默认密钥长度
     */
    public static int getDefaultKeyLength(String algorithm) {
        if (algorithm == null) {
            return 0;
        }

        switch (algorithm.toUpperCase()) {
            case "SM4":
                return 16;
            default:
                return 0;
        }
    }

    /**
     * 获取算法的默认IV长度
     */
    public static int getDefaultIVLength(String algorithm) {
        if (algorithm == null) {
            return 0;
        }

        switch (algorithm.toUpperCase()) {
            case "SM4":
                return 16;
            default:
                return 0;
        }
    }

    /**
     * 验证算法配置的完整性
     */
    public static boolean validateAlgorithmConfig(AlgorithmConfigDTO config) {
        if (config == null || !config.isValid()) {
            return false;
        }

        // 验证算法支持
        if (!isSupportedAlgorithm(config.getAlgorithm())) {
            return false;
        }

        // 验证模式支持
        if (!isSupportedMode(config.getNormalizedMode())) {
            return false;
        }

        // 验证填充支持
        if (!isSupportedPadding(config.getNormalizedPadding())) {
            return false;
        }

        return true;
    }
}
