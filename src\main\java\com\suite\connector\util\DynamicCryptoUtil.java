package com.suite.connector.util;

import com.suite.connector.model.dto.AlgorithmConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.BlockCipher;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.modes.CBCBlockCipher;
import org.bouncycastle.crypto.modes.ECBBlockCipher;
import org.bouncycastle.crypto.paddings.BlockCipherPadding;
import org.bouncycastle.crypto.paddings.PKCS7Padding;
import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;

/**
 * 动态加密工具类
 * 根据算法配置动态选择加密方式
 * 
 * <AUTHOR> Connector Team
 */
@Slf4j
public class DynamicCryptoUtil {

    /**
     * 根据算法配置进行加密
     * 
     * @param plaintext 明文
     * @param key 密钥（十六进制字符串）
     * @param algorithmConfig 算法配置
     * @return 加密后的密文（Base64编码）
     */
    public static String encrypt(String plaintext, String key, AlgorithmConfigDTO algorithmConfig) {
        if (plaintext == null || key == null || algorithmConfig == null) {
            throw new IllegalArgumentException("参数不能为null");
        }

        if (!algorithmConfig.isValid()) {
            throw new IllegalArgumentException("无效的算法配置");
        }

        try {
            byte[] plaintextBytes = plaintext.getBytes(StandardCharsets.UTF_8);
            byte[] keyBytes = Hex.decode(key);
            
            return encrypt(plaintextBytes, keyBytes, algorithmConfig);
            
        } catch (Exception e) {
            log.error("动态加密失败: algorithm={}, mode={}, padding={}", 
                     algorithmConfig.getAlgorithm(), 
                     algorithmConfig.getNormalizedMode(), 
                     algorithmConfig.getNormalizedPadding(), e);
            throw new RuntimeException("动态加密失败", e);
        }
    }

    /**
     * 根据算法配置进行解密
     * 
     * @param ciphertext 密文（Base64编码）
     * @param key 密钥（十六进制字符串）
     * @param algorithmConfig 算法配置
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext, String key, AlgorithmConfigDTO algorithmConfig) {
        if (ciphertext == null || key == null || algorithmConfig == null) {
            throw new IllegalArgumentException("参数不能为null");
        }

        if (!algorithmConfig.isValid()) {
            throw new IllegalArgumentException("无效的算法配置");
        }

        try {
            byte[] ciphertextBytes = Base64.decode(ciphertext);
            byte[] keyBytes = Hex.decode(key);
            
            byte[] decryptedBytes = decrypt(ciphertextBytes, keyBytes, algorithmConfig);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("动态解密失败: algorithm={}, mode={}, padding={}", 
                     algorithmConfig.getAlgorithm(), 
                     algorithmConfig.getNormalizedMode(), 
                     algorithmConfig.getNormalizedPadding(), e);
            throw new RuntimeException("动态解密失败", e);
        }
    }

    /**
     * 字节数组加密
     */
    private static String encrypt(byte[] plaintext, byte[] key, AlgorithmConfigDTO config) {
        PaddedBufferedBlockCipher cipher = createCipher(config);
        
        // 初始化加密参数
        if (config.requiresIV()) {
            String ivHex = config.getEffectiveIV();
            byte[] iv = Hex.decode(ivHex);
            KeyParameter keyParam = new KeyParameter(key);
            ParametersWithIV params = new ParametersWithIV(keyParam, iv);
            cipher.init(true, params);
        } else {
            KeyParameter keyParam = new KeyParameter(key);
            cipher.init(true, keyParam);
        }
        
        // 执行加密
        byte[] ciphertext = new byte[cipher.getOutputSize(plaintext.length)];
        int len = cipher.processBytes(plaintext, 0, plaintext.length, ciphertext, 0);
        len += cipher.doFinal(ciphertext, len);
        
        // 截取实际长度的密文
        byte[] result = new byte[len];
        System.arraycopy(ciphertext, 0, result, 0, len);
        
        return Base64.toBase64String(result);
    }

    /**
     * 字节数组解密
     */
    private static byte[] decrypt(byte[] ciphertext, byte[] key, AlgorithmConfigDTO config) {
        PaddedBufferedBlockCipher cipher = createCipher(config);
        
        // 初始化解密参数
        if (config.requiresIV()) {
            String ivHex = config.getEffectiveIV();
            byte[] iv = Hex.decode(ivHex);
            KeyParameter keyParam = new KeyParameter(key);
            ParametersWithIV params = new ParametersWithIV(keyParam, iv);
            cipher.init(false, params);
        } else {
            KeyParameter keyParam = new KeyParameter(key);
            cipher.init(false, keyParam);
        }
        
        // 执行解密
        byte[] plaintext = new byte[cipher.getOutputSize(ciphertext.length)];
        int len = cipher.processBytes(ciphertext, 0, ciphertext.length, plaintext, 0);
        len += cipher.doFinal(plaintext, len);
        
        // 截取实际长度的明文
        byte[] result = new byte[len];
        System.arraycopy(plaintext, 0, result, 0, len);
        
        return result;
    }

    /**
     * 根据算法配置创建加密器
     */
    private static PaddedBufferedBlockCipher createCipher(AlgorithmConfigDTO config) {
        // 创建基础加密引擎
        BlockCipher engine = createEngine(config.getAlgorithm());
        
        // 创建加密模式
        BlockCipher cipher = createMode(engine, config.getNormalizedMode());
        
        // 创建填充方式
        BlockCipherPadding padding = createPadding(config.getNormalizedPadding());
        
        return new PaddedBufferedBlockCipher(cipher, padding);
    }

    /**
     * 创建加密引擎
     */
    private static BlockCipher createEngine(String algorithm) {
        if (algorithm == null) {
            throw new IllegalArgumentException("算法名称不能为null");
        }
        
        switch (algorithm.toUpperCase()) {
            case "SM4":
                return new SM4Engine();
            default:
                throw new UnsupportedOperationException("不支持的算法: " + algorithm);
        }
    }

    /**
     * 创建加密模式
     */
    private static BlockCipher createMode(BlockCipher engine, String mode) {
        if (mode == null) {
            mode = "ECB"; // 默认ECB模式
        }
        
        switch (mode.toUpperCase()) {
            case "ECB":
                return new ECBBlockCipher(engine);
            case "CBC":
                return new CBCBlockCipher(engine);
            default:
                throw new UnsupportedOperationException("不支持的加密模式: " + mode);
        }
    }

    /**
     * 创建填充方式
     */
    private static BlockCipherPadding createPadding(String padding) {
        if (padding == null) {
            padding = "PKCS7"; // 默认PKCS7填充
        }
        
        switch (padding.toUpperCase()) {
            case "PKCS7":
            case "PKCS5": // PKCS5和PKCS7在块大小为16时是等价的
                return new PKCS7Padding();
            default:
                throw new UnsupportedOperationException("不支持的填充方式: " + padding);
        }
    }

    /**
     * 验证密钥长度是否符合算法要求
     */
    public static boolean isValidKeyLength(String algorithm, byte[] key) {
        if (algorithm == null || key == null) {
            return false;
        }
        
        switch (algorithm.toUpperCase()) {
            case "SM4":
                return key.length == 16; // SM4需要16字节密钥
            default:
                return false;
        }
    }

    /**
     * 验证IV长度是否符合算法要求
     */
    public static boolean isValidIVLength(String algorithm, byte[] iv) {
        if (algorithm == null || iv == null) {
            return false;
        }
        
        switch (algorithm.toUpperCase()) {
            case "SM4":
                return iv.length == 16; // SM4需要16字节IV
            default:
                return false;
        }
    }

    /**
     * 获取算法的默认密钥长度
     */
    public static int getDefaultKeyLength(String algorithm) {
        if (algorithm == null) {
            return 0;
        }
        
        switch (algorithm.toUpperCase()) {
            case "SM4":
                return 16;
            default:
                return 0;
        }
    }

    /**
     * 获取算法的默认IV长度
     */
    public static int getDefaultIVLength(String algorithm) {
        if (algorithm == null) {
            return 0;
        }
        
        switch (algorithm.toUpperCase()) {
            case "SM4":
                return 16;
            default:
                return 0;
        }
    }
}
