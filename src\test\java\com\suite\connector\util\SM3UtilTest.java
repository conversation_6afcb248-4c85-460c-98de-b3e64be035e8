package com.suite.connector.util;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * SM3工具类测试
 * 
 * <AUTHOR> Connector Team
 */
class SM3UtilTest {

    @Test
    void testHashString() {
        // 测试基本哈希功能
        String input = "Hello, SM3!";
        String hash = SM3Util.hash(input);
        
        assertThat(hash).isNotNull();
        assertThat(hash).hasSize(64); // 32字节 = 64个十六进制字符
        assertThat(hash).matches("^[0-9a-f]+$"); // 只包含小写十六进制字符
    }

    @Test
    void testHashConsistency() {
        // 测试相同输入产生相同哈希
        String input = "Test consistency";
        String hash1 = SM3Util.hash(input);
        String hash2 = SM3Util.hash(input);
        
        assertThat(hash1).isEqualTo(hash2);
    }

    @Test
    void testHashDifferentInputs() {
        // 测试不同输入产生不同哈希
        String input1 = "Input 1";
        String input2 = "Input 2";
        String hash1 = SM3Util.hash(input1);
        String hash2 = SM3Util.hash(input2);
        
        assertThat(hash1).isNotEqualTo(hash2);
    }

    @Test
    void testHashEmptyString() {
        // 测试空字符串哈希
        String hash = SM3Util.hash("");
        
        assertThat(hash).isNotNull();
        assertThat(hash).hasSize(64);
    }

    @Test
    void testHashNullInput() {
        // 测试null输入
        assertThatThrownBy(() -> SM3Util.hash((String) null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("不能为null");
    }

    @Test
    void testHashByteArray() {
        // 测试字节数组哈希
        byte[] input = "Hello, SM3!".getBytes();
        String hash = SM3Util.hash(input);
        
        assertThat(hash).isNotNull();
        assertThat(hash).hasSize(64);
    }

    @Test
    void testHashMultipleStrings() {
        // 测试多个字符串拼接哈希
        String hash = SM3Util.hash("Hello", " ", "World", "!");
        String directHash = SM3Util.hash("Hello World!");
        
        assertThat(hash).isEqualTo(directHash);
    }

    @Test
    void testVerifyHash() {
        // 测试哈希验证
        String input = "Test verification";
        String hash = SM3Util.hash(input);
        
        assertThat(SM3Util.verify(input, hash)).isTrue();
        assertThat(SM3Util.verify(input, "wrong_hash")).isFalse();
        assertThat(SM3Util.verify(null, hash)).isFalse();
        assertThat(SM3Util.verify(input, null)).isFalse();
    }

    @Test
    void testHashWithSalt() {
        // 测试带盐值的哈希
        String input = "password";
        String salt = "random_salt";
        String hash1 = SM3Util.hashWithSalt(input, salt);
        String hash2 = SM3Util.hashWithSalt(input, salt);
        String hash3 = SM3Util.hashWithSalt(input, "different_salt");
        
        assertThat(hash1).isEqualTo(hash2); // 相同盐值产生相同哈希
        assertThat(hash1).isNotEqualTo(hash3); // 不同盐值产生不同哈希
    }

    @Test
    void testGenerateSalt() {
        // 测试盐值生成
        String salt1 = SM3Util.generateSalt();
        String salt2 = SM3Util.generateSalt();
        
        assertThat(salt1).isNotNull();
        assertThat(salt2).isNotNull();
        assertThat(salt1).isNotEqualTo(salt2); // 每次生成的盐值应该不同
        assertThat(salt1).hasSize(32); // 默认16字节 = 32个十六进制字符
    }

    @Test
    void testGenerateSaltWithLength() {
        // 测试指定长度的盐值生成
        String salt = SM3Util.generateSalt(8);
        
        assertThat(salt).isNotNull();
        assertThat(salt).hasSize(16); // 8字节 = 16个十六进制字符
    }

    @Test
    void testHmac() {
        // 测试HMAC-SM3
        String key = "secret_key";
        String data = "message_to_authenticate";
        String hmac1 = SM3Util.hmac(key, data);
        String hmac2 = SM3Util.hmac(key, data);
        
        assertThat(hmac1).isNotNull();
        assertThat(hmac1).hasSize(64);
        assertThat(hmac1).isEqualTo(hmac2); // 相同输入产生相同HMAC
        
        // 测试不同密钥产生不同HMAC
        String hmac3 = SM3Util.hmac("different_key", data);
        assertThat(hmac1).isNotEqualTo(hmac3);
    }

    @Test
    void testIsValidHash() {
        // 测试哈希值验证
        String validHash = SM3Util.hash("test");
        String invalidHash1 = "invalid_hash";
        String invalidHash2 = "1234567890abcdef"; // 长度不够
        String invalidHash3 = null;
        
        assertThat(SM3Util.isValidHash(validHash)).isTrue();
        assertThat(SM3Util.isValidHash(invalidHash1)).isFalse();
        assertThat(SM3Util.isValidHash(invalidHash2)).isFalse();
        assertThat(SM3Util.isValidHash(invalidHash3)).isFalse();
    }

    @Test
    void testKnownTestVectors() {
        // 测试已知的测试向量（如果有标准测试向量的话）
        // 这里使用一个简单的测试用例
        String input = "abc";
        String hash = SM3Util.hash(input);
        
        // SM3("abc") 的标准结果
        // 注意：这里需要根据实际的SM3标准测试向量来验证
        assertThat(hash).isNotNull();
        assertThat(hash).hasSize(64);
        
        // 验证一致性
        String hash2 = SM3Util.hash(input);
        assertThat(hash).isEqualTo(hash2);
    }
}
