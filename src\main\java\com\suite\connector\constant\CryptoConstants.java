package com.suite.connector.constant;

/**
 * 加密算法相关常量
 * 
 * <AUTHOR> Connector Team
 */
public final class CryptoConstants {

    /**
     * SM3算法相关常量
     */
    public static final class SM3 {
        /** SM3哈希长度（字节） */
        public static final int HASH_LENGTH_BYTES = 32;
        
        /** SM3哈希长度（十六进制字符串） */
        public static final int HASH_LENGTH_HEX = 64;
        
        /** SM3算法名称 */
        public static final String ALGORITHM_NAME = "SM3";
        
        /** 默认字符编码 */
        public static final String DEFAULT_CHARSET = "UTF-8";
    }

    /**
     * SM4算法相关常量
     */
    public static final class SM4 {
        /** SM4密钥长度（字节） */
        public static final int KEY_LENGTH_BYTES = 16;
        
        /** SM4密钥长度（十六进制字符串） */
        public static final int KEY_LENGTH_HEX = 32;
        
        /** SM4初始化向量长度（字节） */
        public static final int IV_LENGTH_BYTES = 16;
        
        /** SM4初始化向量长度（十六进制字符串） */
        public static final int IV_LENGTH_HEX = 32;
        
        /** SM4分组长度（字节） */
        public static final int BLOCK_SIZE = 16;
        
        /** SM4算法名称 */
        public static final String ALGORITHM_NAME = "SM4";
        
        /** SM4/CBC/PKCS7Padding */
        public static final String TRANSFORMATION = "SM4/CBC/PKCS7Padding";
        
        /** 默认字符编码 */
        public static final String DEFAULT_CHARSET = "UTF-8";
    }

    /**
     * 通用加密常量
     */
    public static final class Common {
        /** 十六进制字符 */
        public static final String HEX_CHARS = "0123456789abcdef";
        
        /** Base64编码字符 */
        public static final String BASE64_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        
        /** 默认盐值长度（字节） */
        public static final int DEFAULT_SALT_LENGTH = 16;
        
        /** 时间戳格式 */
        public static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmss";
        
        /** UUID格式正则表达式 */
        public static final String UUID_PATTERN = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$";
        
        /** 简单UUID格式正则表达式（无连字符） */
        public static final String SIMPLE_UUID_PATTERN = "^[0-9a-fA-F]{32}$";
        
        /** 十六进制字符串正则表达式 */
        public static final String HEX_PATTERN = "^[0-9a-fA-F]+$";
    }

    /**
     * 错误消息常量
     */
    public static final class ErrorMessages {
        public static final String NULL_INPUT = "输入参数不能为null";
        public static final String EMPTY_INPUT = "输入参数不能为空";
        public static final String INVALID_KEY_LENGTH = "密钥长度必须为16字节";
        public static final String INVALID_IV_LENGTH = "初始化向量长度必须为16字节";
        public static final String INVALID_HEX_FORMAT = "无效的十六进制格式";
        public static final String INVALID_UUID_FORMAT = "无效的UUID格式";
        public static final String ENCRYPTION_FAILED = "加密操作失败";
        public static final String DECRYPTION_FAILED = "解密操作失败";
        public static final String HASH_CALCULATION_FAILED = "哈希计算失败";
        public static final String SIGNATURE_VERIFICATION_FAILED = "签名验证失败";
    }

    /**
     * 日志消息常量
     */
    public static final class LogMessages {
        public static final String CRYPTO_PROVIDER_ADDED = "加密提供者已添加: {}";
        public static final String KEY_CALCULATION_START = "开始计算认证key: suiteId={}, uuid={}";
        public static final String KEY_CALCULATION_SUCCESS = "认证key计算成功: length={}";
        public static final String KEY_CALCULATION_FAILED = "认证key计算失败: {}";
        public static final String ENCRYPTION_START = "开始加密数据: dataLength={}";
        public static final String ENCRYPTION_SUCCESS = "数据加密成功: ciphertextLength={}";
        public static final String DECRYPTION_START = "开始解密数据: ciphertextLength={}";
        public static final String DECRYPTION_SUCCESS = "数据解密成功: plaintextLength={}";
        public static final String HASH_CALCULATION_START = "开始计算哈希: inputLength={}";
        public static final String HASH_CALCULATION_SUCCESS = "哈希计算成功: hash={}";
    }

    /**
     * 配置键常量
     */
    public static final class ConfigKeys {
        public static final String CRYPTO_ENABLED = "suite.connector.crypto.enabled";
        public static final String DEFAULT_KEY_LENGTH = "suite.connector.crypto.default-key-length";
        public static final String DEFAULT_SALT_LENGTH = "suite.connector.crypto.default-salt-length";
        public static final String TIMESTAMP_ENABLED = "suite.connector.crypto.timestamp-enabled";
        public static final String SIGNATURE_ENABLED = "suite.connector.crypto.signature-enabled";
    }

    // 私有构造函数，防止实例化
    private CryptoConstants() {
        throw new UnsupportedOperationException("常量类不能被实例化");
    }
}
