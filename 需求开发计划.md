# 套件数据同步服务 - 需求开发计划

## 📋 技术栈选型分析

### ❌ 为什么不用Spring Batch
- Spring Batch适用于**一次性批处理任务**，您需要**周期性循环执行**
- Spring Batch处理**有限数据集**，您需要**持续监控和增量同步**
- Spring Batch是**任务导向**，您的场景是**服务导向**

### ✅ 推荐技术栈
```
核心框架: Spring Boot + Spring Task Scheduling
HTTP客户端: Spring WebFlux (异步非阻塞)
数据库: Spring Data JPA + MySQL/PostgreSQL
内存缓存: ConcurrentHashMap (套件->部门/终端信息映射)
消息队列: Apache Kafka
XML解析: Jackson XML
加密算法: 国密SM3/SM4算法库
容错处理: Resilience4j (熔断、重试)
```

## 🏗️ 系统架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Suite Data Sync Service                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 定时调度器   │  │ 刷新接口     │  │ 监控接口     │         │
│  │ @Scheduled  │  │ /refresh    │  │ /health     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 套件客户端   │  │ 数据同步服务 │  │ 内存缓存     │         │
│  │ SuiteClient │  │ SyncService │  │ MemoryCache │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 国密算法     │  │ XML解析器   │  │ Kafka生产者 │         │
│  │ SM3/SM4     │  │ XMLParser   │  │ Producer    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐                         │
│  │ MySQL       │  │ Kafka       │                         │
│  │ 进度&配置    │  │ 数据主题     │                         │
│  └─────────────┘  └─────────────┘                         │
└─────────────────────────────────────────────────────────────┘
```

## 📊 数据库设计

### suite_offset表 (进度记录)
```sql
CREATE TABLE suite_offset (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    suite_id VARCHAR(64) NOT NULL,
    dept_id VARCHAR(64) NOT NULL,
    host_code VARCHAR(64) NOT NULL,
    data_type VARCHAR(32) NOT NULL,
    sub_type VARCHAR(32) NOT NULL,
    offset_value BIGINT DEFAULT 0,
    first_time DATETIME,
    last_time DATETIME,
    op_status VARCHAR(16) DEFAULT 'SUCCESS',
    err_msg TEXT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_suite_offset (suite_id, dept_id, host_code, data_type, sub_type)
);
```

### datasource_access表 (套件状态)
```sql
CREATE TABLE datasource_access (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    suite_id VARCHAR(64) NOT NULL,
    topic VARCHAR(128) NOT NULL,
    total BIGINT DEFAULT 0,
    status VARCHAR(16) DEFAULT 'SUCCESS',
    first_time DATETIME,
    last_time DATETIME,
    params TEXT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_suite_id (suite_id)
);
```

## 🔄 核心业务流程

### 1. 数据同步主流程
```
1. 获取套件配置 → 2. 计算加密Key → 3. 获取组织架构(缓存)
                                    ↓
8. 记录进度状态 ← 7. 分页拉取数据 ← 6. 计算分页参数 ← 5. 获取数据量
                                    ↓
9. 写入Kafka → 10. 更新汇总状态 → 11. 等待5分钟 → 12. 下一轮
```

### 2. 增量同步策略
- **首次同步**：从头开始，拉取所有数据
- **增量同步**：基于offset，只拉取新增数据
- **内存缓存策略**：每次启动重新拉取组织架构，内存维护套件->部门/终端映射
- **Refresh机制**：清空内存缓存，重新拉取组织架构信息

## 🎯 12阶段开发计划

### 阶段1: 项目初始化和基础架构搭建
- 创建Spring Boot项目，配置Maven依赖
- 设置application.yml配置文件
- 配置数据库连接、Redis、Kafka连接
- 建立基础包结构

### 阶段2: 数据库设计和实体类创建
- 创建suite_offset和datasource_access表
- 开发JPA实体类和Repository接口
- 配置数据库连接池和事务管理

### 阶段3: 国密算法集成
- 集成SM3和SM4算法库
- 实现根据秘钥和UUID计算请求key的工具类
- 编写加密解密的单元测试

### 阶段4: 套件API客户端开发
- 使用WebClient开发动态URL的HTTP客户端
- 实现key参数拼接到URL查询参数中
- 实现算法接口、组织结构、部门终端、数据接口调用
- 添加请求重试和超时机制
- 支持从数据库配置动态获取套件URL

### 阶段5: XML数据解析器开发
- 使用Jackson XML解析套件返回的XML数据
- 创建对应的DTO类和解析器
- 处理各种XML格式的兼容性

### 阶段6: 内存缓存服务开发
- 使用ConcurrentHashMap实现套件->部门/终端信息的内存映射
- 支持缓存清理和重新加载
- 实现线程安全的缓存操作

### 阶段7: Kafka生产者服务开发
- 配置Kafka生产者
- 实现动态主题创建(suite_{ip}_{port})
- 添加消息发送确认和异常处理

### 阶段8: 数据同步核心服务开发
- 实现完整的12步数据拉取流程
- 处理分页逻辑和进度记录
- 实现增量同步机制

### 阶段9: 定时任务调度器开发
- 使用@Scheduled实现5分钟周期任务
- 支持多套件并发处理
- 添加任务状态管理和监控

### 阶段10: 刷新接口开发
- 实现/suite/refresh REST接口
- 支持动态重新加载配置
- 实现任务重启和内存缓存清理

### 阶段11: 异常处理和监控
- 完善全局异常处理机制
- 添加详细的日志记录
- 实现健康检查和监控指标

### 阶段12: 单元测试和集成测试
- 编写核心功能的单元测试
- 创建集成测试环境
- 性能测试和压力测试

## 🚀 项目优势

### 1. 技术优势
- **异步非阻塞**：WebFlux提高并发性能
- **内存缓存**：高效的套件信息映射，无外部依赖
- **增量同步**：避免重复数据传输
- **容错机制**：保证系统稳定性

### 2. 业务优势
- **实时性**：5分钟周期，准实时数据同步
- **可扩展**：支持多套件并发处理
- **可维护**：模块化设计，便于维护
- **可监控**：完善的日志和监控体系

## � HTTP客户端技术选型

### 方案对比

| 特性 | WebClient | OpenFeign |
|------|-----------|-----------|
| 动态URL | ✅ 完全支持 | ⚠️ 需要特殊处理 |
| 异步支持 | ✅ 原生支持 | ❌ 同步调用 |
| 配置灵活性 | ✅ 高度灵活 | ⚠️ 相对固定 |
| 代码简洁性 | ⚠️ 需要手动构建 | ✅ 声明式调用 |
| XML处理 | ✅ 灵活处理 | ✅ 自动转换 |
| 学习成本 | ⚠️ 中等 | ✅ 较低 |

### 确定方案：WebClient + 参数拼接Key
```java
@Service
public class SuiteHttpClient {

    private final WebClient.Builder webClientBuilder;
    private final CryptoService cryptoService;

    public Mono<String> callSuiteApi(String baseUrl, String path,
                                   Map<String, String> params,
                                   String secret, String uuid) {
        // 计算认证key
        String authKey = cryptoService.calculateKey(secret, uuid);

        return webClientBuilder.baseUrl(baseUrl)
            .build()
            .get()
            .uri(uriBuilder -> {
                UriBuilder builder = uriBuilder.path(path);
                // 添加业务参数
                params.forEach(builder::queryParam);
                // 拼接认证key参数
                builder.queryParam("key", authKey);
                return builder.build();
            })
            .retrieve()
            .bodyToMono(String.class)
            .retryWhen(Retry.backoff(3, Duration.ofSeconds(1)));
    }
}
```

### 使用示例
```java
@Service
public class SuiteDataSyncService {

    private final SuiteHttpClient httpClient;

    public Mono<String> getAlgorithm(SuiteConfig config) {
        Map<String, String> params = new HashMap<>();
        // 添加业务参数
        params.put("version", "1.0");

        return httpClient.callSuiteApi(
            config.getBaseUrl(),
            "/api/algorithm",
            params,
            config.getSecret(),
            UUID.randomUUID().toString()
        );
    }

    public Mono<String> getOrgStructure(SuiteConfig config) {
        Map<String, String> params = new HashMap<>();

        return httpClient.callSuiteApi(
            config.getBaseUrl(),
            "/api/org/structure",
            params,
            config.getSecret(),
            UUID.randomUUID().toString()
        );
    }
}
```

## �📝 关键技术点

### 1. 国密算法实现
- SM3哈希算法用于数据完整性校验
- SM4对称加密算法用于敏感数据加密
- 结合秘钥和UUID生成请求认证key

### 2. 增量同步机制
- 基于offset记录每个数据类型的同步进度
- 支持断点续传，异常恢复后从上次位置继续
- 智能判断是否需要重新获取组织架构信息

### 3. 内存缓存策略
- 使用ConcurrentHashMap缓存套件->部门/终端信息映射
- 每次启动重新拉取组织架构，确保数据最新
- Refresh操作时清空内存缓存并重新加载

### 4. 容错处理
- HTTP请求重试机制
- 熔断器防止雪崩效应
- 详细的错误日志和状态记录

这个方案完全避开了Spring Batch的限制，采用更适合周期性数据同步场景的技术栈，既满足了业务需求，又保证了系统的性能和稳定性。
