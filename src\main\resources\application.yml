server:
  port: 8080
  servlet:
    context-path: /suite-connector

spring:
  application:
    name: suite-connector
  
  # 数据库配置
  datasource:
    url: ****************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # Kafka配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
  
  # WebFlux配置
  webflux:
    base-path: /api

# Resilience4j配置
resilience4j:
  circuitbreaker:
    instances:
      suite-api:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        automatic-transition-from-open-to-half-open-enabled: true
  
  retry:
    instances:
      suite-api:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
        retry-exceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - org.springframework.web.reactive.function.client.WebClientRequestException
  
  timelimiter:
    instances:
      suite-api:
        timeout-duration: 30s

# Spring Cloud LoadBalancer配置
spring:
  cloud:
    loadbalancer:
      retry:
        enabled: true
        max-retries-on-same-service-instance: 1
        max-retries-on-next-service-instance: 2

# 套件连接器自定义配置
suite:
  connector:
    # 定时任务配置
    scheduler:
      sync-interval: 300000  # 5分钟 = 300000毫秒
      initial-delay: 30000   # 启动后30秒开始执行
      thread-pool-size: 10   # 线程池大小
    
    # HTTP客户端配置
    http:
      connect-timeout: 10s
      read-timeout: 30s
      write-timeout: 30s
      max-connections: 100
      max-connections-per-route: 20
    
    # 分页配置
    pagination:
      default-page-size: 1000
      max-page-size: 5000

# 日志配置
logging:
  level:
    com.suite.connector: INFO
    org.springframework.web.reactive.function.client: DEBUG
    org.springframework.kafka: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
  file:
    name: logs/suite-connector.log
    max-size: 100MB
    max-history: 30

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
