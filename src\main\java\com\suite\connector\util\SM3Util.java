package com.suite.connector.util;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * SM3哈希算法工具类
 * SM3是中国国家密码管理局发布的密码杂凑算法标准
 * 
 * <AUTHOR> Connector Team
 */
@Slf4j
public class SM3Util {

    /**
     * SM3哈希长度（字节）
     */
    public static final int HASH_LENGTH = 32;

    /**
     * SM3哈希长度（十六进制字符串）
     */
    public static final int HEX_HASH_LENGTH = 64;

    /**
     * 计算字符串的SM3哈希值
     * 
     * @param input 输入字符串
     * @return SM3哈希值（十六进制字符串）
     */
    public static String hash(String input) {
        if (input == null) {
            throw new IllegalArgumentException("输入字符串不能为null");
        }
        
        return hash(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 计算字节数组的SM3哈希值
     * 
     * @param input 输入字节数组
     * @return SM3哈希值（十六进制字符串）
     */
    public static String hash(byte[] input) {
        if (input == null) {
            throw new IllegalArgumentException("输入字节数组不能为null");
        }

        try {
            SM3Digest digest = new SM3Digest();
            digest.update(input, 0, input.length);
            
            byte[] hashBytes = new byte[HASH_LENGTH];
            digest.doFinal(hashBytes, 0);
            
            return Hex.toHexString(hashBytes).toLowerCase();
        } catch (Exception e) {
            log.error("SM3哈希计算失败", e);
            throw new RuntimeException("SM3哈希计算失败", e);
        }
    }

    /**
     * 计算多个字符串拼接后的SM3哈希值
     * 
     * @param inputs 输入字符串数组
     * @return SM3哈希值（十六进制字符串）
     */
    public static String hash(String... inputs) {
        if (inputs == null || inputs.length == 0) {
            throw new IllegalArgumentException("输入参数不能为空");
        }

        StringBuilder combined = new StringBuilder();
        for (String input : inputs) {
            if (input != null) {
                combined.append(input);
            }
        }
        
        return hash(combined.toString());
    }

    /**
     * 验证哈希值
     * 
     * @param input 原始输入
     * @param expectedHash 期望的哈希值
     * @return 是否匹配
     */
    public static boolean verify(String input, String expectedHash) {
        if (input == null || expectedHash == null) {
            return false;
        }
        
        String actualHash = hash(input);
        return actualHash.equalsIgnoreCase(expectedHash);
    }

    /**
     * 生成带盐值的哈希
     * 
     * @param input 输入字符串
     * @param salt 盐值
     * @return SM3哈希值（十六进制字符串）
     */
    public static String hashWithSalt(String input, String salt) {
        if (input == null || salt == null) {
            throw new IllegalArgumentException("输入和盐值都不能为null");
        }
        
        return hash(input + salt);
    }

    /**
     * 生成随机盐值
     * 
     * @param length 盐值长度（字节）
     * @return 随机盐值（十六进制字符串）
     */
    public static String generateSalt(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("盐值长度必须大于0");
        }
        
        SecureRandom random = new SecureRandom();
        byte[] saltBytes = new byte[length];
        random.nextBytes(saltBytes);
        
        return Hex.toHexString(saltBytes).toLowerCase();
    }

    /**
     * 生成默认长度的随机盐值（16字节）
     * 
     * @return 随机盐值（十六进制字符串）
     */
    public static String generateSalt() {
        return generateSalt(16);
    }

    /**
     * HMAC-SM3算法实现
     * 
     * @param key 密钥
     * @param data 数据
     * @return HMAC-SM3值（十六进制字符串）
     */
    public static String hmac(String key, String data) {
        if (key == null || data == null) {
            throw new IllegalArgumentException("密钥和数据都不能为null");
        }
        
        return hmac(key.getBytes(StandardCharsets.UTF_8), data.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * HMAC-SM3算法实现
     * 
     * @param key 密钥字节数组
     * @param data 数据字节数组
     * @return HMAC-SM3值（十六进制字符串）
     */
    public static String hmac(byte[] key, byte[] data) {
        if (key == null || data == null) {
            throw new IllegalArgumentException("密钥和数据都不能为null");
        }

        try {
            // HMAC-SM3实现
            byte[] ipad = new byte[64];
            byte[] opad = new byte[64];
            
            // 如果密钥长度大于64字节，先进行哈希
            if (key.length > 64) {
                key = Hex.decode(hash(key));
            }
            
            // 如果密钥长度小于64字节，用0填充
            if (key.length < 64) {
                byte[] paddedKey = new byte[64];
                System.arraycopy(key, 0, paddedKey, 0, key.length);
                key = paddedKey;
            }
            
            // 生成ipad和opad
            for (int i = 0; i < 64; i++) {
                ipad[i] = (byte) (key[i] ^ 0x36);
                opad[i] = (byte) (key[i] ^ 0x5C);
            }
            
            // 计算内层哈希
            byte[] innerData = new byte[ipad.length + data.length];
            System.arraycopy(ipad, 0, innerData, 0, ipad.length);
            System.arraycopy(data, 0, innerData, ipad.length, data.length);
            String innerHash = hash(innerData);
            
            // 计算外层哈希
            byte[] outerData = new byte[opad.length + HASH_LENGTH];
            System.arraycopy(opad, 0, outerData, 0, opad.length);
            System.arraycopy(Hex.decode(innerHash), 0, outerData, opad.length, HASH_LENGTH);
            
            return hash(outerData);
            
        } catch (Exception e) {
            log.error("HMAC-SM3计算失败", e);
            throw new RuntimeException("HMAC-SM3计算失败", e);
        }
    }

    /**
     * 验证输入是否为有效的SM3哈希值
     * 
     * @param hash 哈希值字符串
     * @return 是否为有效的SM3哈希值
     */
    public static boolean isValidHash(String hash) {
        if (hash == null) {
            return false;
        }
        
        // 检查长度
        if (hash.length() != HEX_HASH_LENGTH) {
            return false;
        }
        
        // 检查是否为十六进制字符串
        return hash.matches("^[0-9a-fA-F]+$");
    }
}
