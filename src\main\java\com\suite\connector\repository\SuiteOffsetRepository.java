package com.suite.connector.repository;

import com.suite.connector.model.entity.SuiteOffset;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 套件同步进度数据访问接口
 * 
 * <AUTHOR> Connector Team
 */
@Repository
public interface SuiteOffsetRepository extends JpaRepository<SuiteOffset, Long> {

    /**
     * 根据套件ID查找所有进度记录
     */
    List<SuiteOffset> findBySuiteId(String suiteId);

    /**
     * 根据套件ID和状态查找进度记录
     */
    List<SuiteOffset> findBySuiteIdAndOpStatus(String suiteId, String opStatus);

    /**
     * 查找特定的进度记录
     */
    Optional<SuiteOffset> findBySuiteIdAndDeptIdAndHostCodeAndDataTypeAndSubType(
            String suiteId, String deptId, String hostCode, String dataType, String subType);

    /**
     * 根据套件ID和部门ID查找进度记录
     */
    List<SuiteOffset> findBySuiteIdAndDeptId(String suiteId, String deptId);

    /**
     * 根据套件ID和终端代码查找进度记录
     */
    List<SuiteOffset> findBySuiteIdAndHostCode(String suiteId, String hostCode);

    /**
     * 查找失败的同步记录
     */
    @Query("SELECT so FROM SuiteOffset so WHERE so.opStatus = 'FAILED' AND so.lastTime >= :since")
    List<SuiteOffset> findFailedRecordsSince(@Param("since") LocalDateTime since);

    /**
     * 查找正在处理中的记录
     */
    List<SuiteOffset> findByOpStatus(String opStatus);

    /**
     * 查找超时的处理中记录（可能是僵尸进程）
     */
    @Query("SELECT so FROM SuiteOffset so WHERE so.opStatus = 'PROCESSING' AND so.updatedTime < :timeout")
    List<SuiteOffset> findTimeoutProcessingRecords(@Param("timeout") LocalDateTime timeout);

    /**
     * 批量更新状态为失败
     */
    @Modifying
    @Query("UPDATE SuiteOffset so SET so.opStatus = 'FAILED', so.errMsg = :errorMsg, so.updatedTime = :updateTime " +
           "WHERE so.suiteId = :suiteId")
    int markAllAsFailed(@Param("suiteId") String suiteId, 
                       @Param("errorMsg") String errorMsg, 
                       @Param("updateTime") LocalDateTime updateTime);

    /**
     * 重置超时的处理中记录
     */
    @Modifying
    @Query("UPDATE SuiteOffset so SET so.opStatus = 'FAILED', so.errMsg = 'Processing timeout', " +
           "so.updatedTime = :updateTime WHERE so.opStatus = 'PROCESSING' AND so.updatedTime < :timeout")
    int resetTimeoutProcessingRecords(@Param("timeout") LocalDateTime timeout, 
                                    @Param("updateTime") LocalDateTime updateTime);

    /**
     * 获取套件的同步统计信息
     */
    @Query("SELECT so.opStatus, COUNT(so) FROM SuiteOffset so WHERE so.suiteId = :suiteId GROUP BY so.opStatus")
    List<Object[]> getSyncStatistics(@Param("suiteId") String suiteId);

    /**
     * 获取最近同步的记录
     */
    @Query("SELECT so FROM SuiteOffset so WHERE so.suiteId = :suiteId ORDER BY so.lastTime DESC")
    List<SuiteOffset> findRecentSyncRecords(@Param("suiteId") String suiteId);

    /**
     * 删除套件的所有进度记录
     */
    void deleteBySuiteId(String suiteId);

    /**
     * 检查是否存在特定的进度记录
     */
    boolean existsBySuiteIdAndDeptIdAndHostCodeAndDataTypeAndSubType(
            String suiteId, String deptId, String hostCode, String dataType, String subType);

    /**
     * 获取套件的总偏移量
     */
    @Query("SELECT SUM(so.offsetValue) FROM SuiteOffset so WHERE so.suiteId = :suiteId AND so.opStatus = 'SUCCESS'")
    Long getTotalOffsetBySuiteId(@Param("suiteId") String suiteId);
}
